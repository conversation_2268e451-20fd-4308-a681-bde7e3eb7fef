/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#include "PluginProcessor.h"
#include "PluginEditor.h"
#include "SidebarComponent.h"
#include "MainPanelComponent.h"
// #include "PlayerBarComponent.h"


extern void setupAudioDeviceManager();
//==============================================================================
AIpluginAudioProcessorEditor::AIpluginAudioProcessorEditor(AIpluginAudioProcessor &p)
    : AudioProcessorEditor(&p), audioProcessor(p),
      mainPanel()
{
  // Make sure that before the constructor has finished, you've set the
  // editor's size to whatever you need it to be.
  setSize(900, 600);

  addAndMakeVisible(mainPanel);

  addAndMakeVisible(gainSlider);
}

AIpluginAudioProcessorEditor::~AIpluginAudioProcessorEditor()
{
}

//==============================================================================
void AIpluginAudioProcessorEditor::paint(juce::Graphics &g)
{
  // (Our component is opaque, so we must completely fill the background with a solid colour)
  g.fillAll(juce::Colours::darkgrey);
}

void AIpluginAudioProcessorEditor::resized()
{
  // This is generally where you'll want to lay out the positions of any
  // subcomponents in your editor..

  mainPanel.setBounds(0, 0, getWidth(), getHeight());
}
