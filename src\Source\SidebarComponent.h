#pragma once
#include <JuceHeader.h>

// Custom button with icon, bold font, and selected state
class SidebarButton : public juce::TextButton
{
public:
    SidebarButton(const juce::String& text, const juce::Image& iconToUse);
    void setSelected(bool shouldBeSelected);
    void paintButton(juce::Graphics& g, bool isMouseOver, bool isButtonDown) override;

private:
    juce::Image icon;
    bool selected = false;
};

class SidebarComponent : public juce::Component
{
public:
    std::function<void(int)> onButtonClicked;

    SidebarComponent();
    void paint(juce::Graphics& g) override;
    void resized() override;
    void setSelectedIndex(int idx);

private:
    int selectedIndex = -1;

    juce::Image iconCreate, iconLibrary, iconTrain, iconKeyBpm, iconNoise, iconSplit, iconMix;
    std::unique_ptr<SidebarButton> createButton, libraryButton, trainButton, keyBpmButton, noiseButton, splitButton, mixButton;

    void handleButtonClicked(int idx);
    void updateButtonSelection();
};