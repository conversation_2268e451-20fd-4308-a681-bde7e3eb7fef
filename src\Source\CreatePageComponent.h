#pragma once
#include <JuceHeader.h>

class CreatePageComponent : public juce::Component
{
public:
    CreatePageComponent()
    {
        // Add and configure your UI elements here
        addAndMakeVisible(selectVoiceBox);
        addAndMakeVisible(uploadAudioBox);
    }

    void resized() override
    {
        int margin = 16;
        int boxWidth = getWidth() - 2 * margin;
        int totalHeight = getHeight() - 3 * margin;

        // Give more height to the voice part (e.g., 2/5 for voice, 3/5 for upload)
        int voiceHeight = (totalHeight * 2) / 5;
        int uploadHeight = totalHeight - voiceHeight;

        selectVoiceBox.setBounds(margin, margin, boxWidth, voiceHeight);
        uploadAudioBox.setBounds(margin, margin * 2 + voiceHeight, boxWidth, uploadHeight);
    }
    void paint(juce::Graphics &g) override
    {
        // Draw background if needed
    }

private:
    struct VoiceBox : public juce::Component
    {
        void paint(juce::Graphics &g) override
        {
            g.setColour(juce::Colours::white.withAlpha(0.08f));
            g.fillRoundedRectangle(getLocalBounds().toFloat(), 12.0f);
            g.setColour(juce::Colours::white.withAlpha(0.2f));
            g.drawRoundedRectangle(getLocalBounds().toFloat(), 12.0f, 2.0f);

            // Dynamically size the plus icon
            int iconPadding = 16;
            int iconSize = getHeight() - 2 * iconPadding;
            if (iconSize > getWidth() - 2 * iconPadding)
                iconSize = getWidth() - 2 * iconPadding;
            int iconX = iconPadding;
            int iconY = (getHeight() - iconSize) / 2;

            // Draw dotted border for plus icon
            g.setColour(juce::Colours::white.withAlpha(0.5f));
            float dashLengths[] = {4.0f, 4.0f};
            juce::Path dottedRect;
            dottedRect.addRectangle((float)iconX, (float)iconY, (float)iconSize, (float)iconSize);

            juce::Path dashedPath;
            juce::PathStrokeType(2.0f).createDashedStroke(dashedPath, dottedRect, dashLengths, 2);
            g.strokePath(dashedPath, juce::PathStrokeType(2.0f));

            float centerX = iconX + iconSize / 2.0f;
            float centerY = iconY + iconSize / 2.0f;
            float halfLen = iconSize * 0.3f;

            g.drawLine(centerX, centerY - halfLen, centerX, centerY + halfLen, 2.5f);
            g.drawLine(centerX - halfLen, centerY, centerX + halfLen, centerY, 2.5f);

            // Draw text
            g.setColour(juce::Colours::white);
            g.setFont(juce::Font(28.0f, juce::Font::bold)); // Bolder and higher
            g.drawText("Select a voice", iconX + iconSize + 20, 0, getWidth() - (iconX + iconSize + 20), getHeight(), juce::Justification::centredLeft);
        }
    } selectVoiceBox;

    struct UploadBox : public juce::Component
    {
        void paint(juce::Graphics &g) override
        {
            int margin = 12;      // Reduced margin for more content space
            int innerMargin = 12; // Reduced inner margin
            int iconSize = 36;
            int titleHeight = 32;
            int subtitleHeight = 22;
            int spacing = 8;

            // Outer solid border (line)
            g.setColour(juce::Colours::white.withAlpha(0.25f));
            g.drawRoundedRectangle(getLocalBounds().toFloat(), 12.0f, 2.0f);

            // Inner dotted border (inset)
            juce::Rectangle<float> innerBounds = getLocalBounds().reduced(innerMargin).toFloat();
            g.setColour(juce::Colours::white.withAlpha(0.5f));
            float dashLengths[] = {4.0f, 4.0f};
            juce::Path innerRect;
            innerRect.addRoundedRectangle(innerBounds, 10.0f);
            juce::Path innerDashed;
            juce::PathStrokeType(2.0f).createDashedStroke(innerDashed, innerRect, dashLengths, 2);
            g.strokePath(innerDashed, juce::PathStrokeType(2.0f));

            // Fill background inside inner border
            g.setColour(juce::Colours::white.withAlpha(0.08f));
            g.fillRoundedRectangle(innerBounds, 10.0f);

            // Layout inside inner border
            int contentX = (int)innerBounds.getX();
            int contentY = (int)innerBounds.getY();
            int contentW = (int)innerBounds.getWidth();

            // Title
            int textX = contentX + margin;
            int textWidth = contentW - 2 * margin;
            int iconX = contentX + contentW - margin - iconSize;
            int iconY = contentY + margin;
            g.setColour(juce::Colours::white);
            g.setFont(juce::Font(28.0f, juce::Font::bold));
            g.drawText("Upload Audio", textX, iconY, textWidth, titleHeight, juce::Justification::left);

            // // Draw line between title and subtitle/upload icon
            // int lineY = iconY + titleHeight + spacing / 2;
            // g.setColour(juce::Colours::white.withAlpha(0.18f));
            // g.drawLine((float)textX, (float)lineY, (float)(contentX + contentW - margin), (float)lineY, 1.5f);

            // Subtitle
            int subtitleY = iconY + titleHeight + spacing;
            g.setFont(18.0f);
            g.setColour(juce::Colours::white.withAlpha(0.7f));
            g.drawText("Or Drag And Drop Mp3/Wav File Here.", textX, subtitleY, textWidth - iconSize - spacing, subtitleHeight, juce::Justification::left);

            // Upload icon (arrow up), right-aligned with subtitle
            juce::Path arrow;
            int arrowCenterX = iconX + iconSize / 2;
            int arrowCenterY = subtitleY + subtitleHeight / 2 + 2;
            arrow.startNewSubPath(arrowCenterX - 8, arrowCenterY + 8);
            arrow.lineTo(arrowCenterX + 8, arrowCenterY + 8);
            arrow.lineTo(arrowCenterX, arrowCenterY - 8);
            arrow.closeSubPath();
            g.setColour(juce::Colours::white.withAlpha(0.7f));
            g.fillPath(arrow);

            // Draw line between subtitle/upload icon and plus icon
            int plusLineY = subtitleY + subtitleHeight + spacing;
            g.setColour(juce::Colours::white.withAlpha(0.18f));
            g.drawLine((float)textX, (float)plusLineY, (float)(contentX + contentW - margin), (float)plusLineY, 1.5f);

            // Plus icon (left aligned, below the line)
            int plusY = plusLineY + spacing;
            int plusX = textX;

            // Solid border for plus icon
            g.setColour(juce::Colours::white.withAlpha(0.5f));
            g.drawRect(plusX, plusY, iconSize, iconSize, 2);

            // Plus sign
            float centerX = plusX + iconSize / 2.0f;
            float centerY = plusY + iconSize / 2.0f;
            float halfLen = iconSize * 0.3f;
            g.drawLine(centerX, centerY - halfLen, centerX, centerY + halfLen, 2.5f);
            g.drawLine(centerX - halfLen, centerY, centerX + halfLen, centerY, 2.5f);
        }
    } uploadAudioBox;
};