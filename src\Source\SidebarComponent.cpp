#include "SidebarComponent.h"
#include "BinaryData.h"

// SidebarButton implementation
SidebarButton::SidebarButton(const juce::String& text, const juce::Image& iconToUse)
    : juce::TextButton(text), icon(iconToUse)
{
}

void SidebarButton::setSelected(bool shouldBeSelected)
{
    selected = shouldBeSelected;
    repaint();
}

void SidebarButton::paintButton(juce::Graphics& g, bool isMouseOver, bool isButtonDown)
{
    if (selected)
        g.fillAll(juce::Colour::fromRGB(108, 0, 246).darker(0.2f));
    else if (isButtonDown)
        g.fillAll(juce::Colours::purple.darker());
    else if (isMouseOver)
        g.fillAll(juce::Colours::purple.withAlpha(0.3f));

    if (!icon.isNull())
        g.drawImageWithin(icon, 8, 8, 24, 24, juce::RectanglePlacement::centred);

    g.setColour(juce::Colours::white);
    g.setFont(juce::Font(18.0f, juce::Font::bold));
    g.drawText(getButtonText(), 40, 0, getWidth() - 48, getHeight(), juce::Justification::centredLeft);
}

// SidebarComponent implementation
SidebarComponent::SidebarComponent()
{
    iconCreate  = juce::ImageCache::getFromMemory(BinaryData::icon_create_png,  BinaryData::icon_create_pngSize);
    iconLibrary = juce::ImageCache::getFromMemory(BinaryData::icon_library_png, BinaryData::icon_library_pngSize);
    iconTrain   = juce::ImageCache::getFromMemory(BinaryData::icon_train_png,   BinaryData::icon_train_pngSize);
    iconKeyBpm  = juce::ImageCache::getFromMemory(BinaryData::icon_keybpm_png,  BinaryData::icon_keybpm_pngSize);
    iconNoise   = juce::ImageCache::getFromMemory(BinaryData::icon_noise_png,   BinaryData::icon_noise_pngSize);
    iconSplit   = juce::ImageCache::getFromMemory(BinaryData::icon_split_png,   BinaryData::icon_split_pngSize);
    iconMix     = juce::ImageCache::getFromMemory(BinaryData::icon_mix_png,     BinaryData::icon_mix_pngSize);

    createButton   = std::make_unique<SidebarButton>("Create", iconCreate);
    libraryButton  = std::make_unique<SidebarButton>("Library", iconLibrary);
    trainButton    = std::make_unique<SidebarButton>("Train Clone", iconTrain);
    keyBpmButton   = std::make_unique<SidebarButton>("Key/BPM Finder", iconKeyBpm);
    noiseButton    = std::make_unique<SidebarButton>("Noise Remover", iconNoise);
    splitButton    = std::make_unique<SidebarButton>("Split Stems", iconSplit);
    mixButton      = std::make_unique<SidebarButton>("Mix and Master", iconMix);

    addAndMakeVisible(*createButton);
    addAndMakeVisible(*libraryButton);
    addAndMakeVisible(*trainButton);
    addAndMakeVisible(*keyBpmButton);
    addAndMakeVisible(*noiseButton);
    addAndMakeVisible(*splitButton);
    addAndMakeVisible(*mixButton);

    createButton->onClick   = [this] { handleButtonClicked(0); };
    libraryButton->onClick  = [this] { handleButtonClicked(1); };
    trainButton->onClick    = [this] { handleButtonClicked(2); };
    keyBpmButton->onClick   = [this] { handleButtonClicked(3); };
    noiseButton->onClick    = [this] { handleButtonClicked(4); };
    splitButton->onClick    = [this] { handleButtonClicked(5); };
    mixButton->onClick      = [this] { handleButtonClicked(6); };

    setSelectedIndex(0);
}

void SidebarComponent::paint(juce::Graphics& g)
{
    juce::ColourGradient gradient(
        juce::Colour::fromRGB(108, 0, 246), 0, 0,
        juce::Colour::fromRGB(144, 12, 236), 0, (float)getHeight(), false);
    g.setGradientFill(gradient);
    g.fillAll();
}

void SidebarComponent::resized()
{
    int y = 100;
    int buttonHeight = 40;
    int buttonWidth = getWidth() - 20;
    int x = 10;

    createButton->setBounds(x, y, buttonWidth, buttonHeight);      y += buttonHeight + 8;
    libraryButton->setBounds(x, y, buttonWidth, buttonHeight);     y += buttonHeight + 8;
    trainButton->setBounds(x, y, buttonWidth, buttonHeight);       y += buttonHeight + 8;
    keyBpmButton->setBounds(x, y, buttonWidth, buttonHeight);      y += buttonHeight + 8;
    noiseButton->setBounds(x, y, buttonWidth, buttonHeight);       y += buttonHeight + 8;
    splitButton->setBounds(x, y, buttonWidth, buttonHeight);       y += buttonHeight + 8;
    mixButton->setBounds(x, y, buttonWidth, buttonHeight);
}

void SidebarComponent::setSelectedIndex(int idx)
{
    selectedIndex = idx;
    updateButtonSelection();
}

void SidebarComponent::handleButtonClicked(int idx)
{
    setSelectedIndex(idx);
    if (onButtonClicked) onButtonClicked(idx);
}

void SidebarComponent::updateButtonSelection()
{
    createButton->setSelected(selectedIndex == 0);
    libraryButton->setSelected(selectedIndex == 1);
    trainButton->setSelected(selectedIndex == 2);
    keyBpmButton->setSelected(selectedIndex == 3);
    noiseButton->setSelected(selectedIndex == 4);
    splitButton->setSelected(selectedIndex == 5);
    mixButton->setSelected(selectedIndex == 6);
}