#pragma once
#include <JuceHeader.h>

class RecordingButton : public juce::TextButton
{
public:
    RecordingButton(const juce::String &text);
    void paintButton(juce::Graphics &g, bool isMouseOver, bool isButtonDown) override;
};

class TrainClonePageComponent : public juce::Component
{
public:
    TrainClonePageComponent();
    void paint(juce::Graphics &g) override;
    void resized() override;

private:
    void updateButtonColours();

    RecordingButton startRecordingButton{"Start Recording"};
    juce::TextButton uploadAudioButton;
    juce::Image micImage;

    int selectedButton = -1; // -1: none, 0: startRecording, 1: uploadAudio
};