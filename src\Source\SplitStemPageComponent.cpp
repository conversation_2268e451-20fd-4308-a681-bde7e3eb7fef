#include "SplitStemPageComponent.h"
#include "BinaryData.h"
#include "Constant.h"

SplitSteamPageComponent::SplitSteamPageComponent()
    : splitStemsButton("Split Stems")
{
    // Upload area
    uploadTitleLabel.setText("Upload Audio", juce::dontSendNotification);
    uploadTitleLabel.setFont(juce::Font(20.0f, juce::Font::bold));
    uploadTitleLabel.setJustificationType(juce::Justification::centredLeft);
    uploadTitleLabel.setColour(juce::Label::textColourId, juce::Colours::black);
    addAndMakeVisible(uploadTitleLabel);

    uploadSubtitleLabel.setText("or drag and drop mp3/wav file here. 20MB Limit", juce::dontSendNotification);
    uploadSubtitleLabel.setFont(juce::Font(14.0f));
    uploadSubtitleLabel.setJustificationType(juce::Justification::centredLeft);
    uploadSubtitleLabel.setColour(juce::Label::textColourId, juce::Colours::black);
    addAndMakeVisible(uploadSubtitleLabel);

    icon_upload = juce::Drawable::createFromImageData(BinaryData::icon_upload_png, BinaryData::icon_upload_pngSize);
    uploadIconButton.setIcon(std::unique_ptr<juce::Drawable>(icon_upload->createCopy()));
    uploadIconButton.setTooltip("Upload");
    uploadIconButton.setColour(juce::TextButton::buttonColourId, juce::Colour(0xFFd6d7d9));
    uploadIconButton.addListener(this); // <-- Add this line!
    addAndMakeVisible(uploadIconButton);

    // Instrument icons
    icon_guitar = juce::Drawable::createFromImageData(BinaryData::icon_guitar_png, BinaryData::icon_guitar_pngSize);
    icon_piano = juce::Drawable::createFromImageData(BinaryData::icon_piano_png, BinaryData::icon_piano_pngSize);
    icon_vocals = juce::Drawable::createFromImageData(BinaryData::icon_vocal_png, BinaryData::icon_vocal_pngSize);
    icon_bass = juce::Drawable::createFromImageData(BinaryData::icon_bass_png, BinaryData::icon_bass_pngSize);
    icon_drums = juce::Drawable::createFromImageData(BinaryData::icon_drum_png, BinaryData::icon_drum_pngSize);
    icon_other = juce::Drawable::createFromImageData(BinaryData::icon_other_png, BinaryData::icon_other_pngSize);

    setupIconButton(guitarButton, icon_guitar.get(), "Guitar");
    setupIconButton(pianoButton, icon_piano.get(), "Piano");
    setupIconButton(vocalsButton, icon_vocals.get(), "Vocals");
    setupIconButton(bassButton, icon_bass.get(), "Bass");
    setupIconButton(drumsButton, icon_drums.get(), "Drums");
    setupIconButton(otherButton, icon_other.get(), "Other");

    // Split Stems button
    icon_mini_music = juce::Drawable::createFromImageData(BinaryData::icon_mini_music_png, BinaryData::icon_mini_music_pngSize);
    splitStemsButton.setIcon(std::unique_ptr<juce::Drawable>(icon_mini_music->createCopy()));
    splitStemsButton.setTooltip("Split Stems");
    splitStemsButton.setColour(juce::TextButton::buttonColourId, juce::Colour(0xFFd6d7d9));
    splitStemsButton.setColour(juce::TextButton::textColourOffId, juce::Colour(0xFF424752));
    splitStemsButton.addListener(this);
    addAndMakeVisible(splitStemsButton);

    juce::MessageManager::callAsync([this]()
    {
        guitarButton.setEnabled(false);
        pianoButton.setEnabled(false);
        vocalsButton.setEnabled(false);
        bassButton.setEnabled(false);
        drumsButton.setEnabled(false);
        otherButton.setEnabled(false);
    });
}

SplitSteamPageComponent::~SplitSteamPageComponent() = default;

void SplitSteamPageComponent::buttonClicked(juce::Button* button)
{
    if (button == &uploadIconButton /* && !busy */) // Add your busy flag if needed
    {
        fileChooser = std::make_unique<juce::FileChooser>(
            "Select an audio file...",
            juce::File{},
            "*.wav;*.mp3;*.flac",
            false);

        fileChooser->launchAsync(
            static_cast<int>(juce::FileBrowserComponent::openMode)
            | static_cast<int>(juce::FileBrowserComponent::canSelectFiles),
            [this](const juce::FileChooser& fc)
            {
                auto file = fc.getResult();
                if (file.existsAsFile())
                {
                    selectedFile = file;
                    juce::MessageManager::callAsync([file]()
                    {
                        juce::AlertWindow::showMessageBoxAsync(
                            juce::AlertWindow::InfoIcon,
                            "Selected File",
                            "You selected:\n" + file.getFullPathName());
                    });
                    // startSplitStemProcess(file);
                }
                fileChooser.reset();
            });
    }
    else if (button == &guitarButton)  requestSplitStems(uploadedFileId, "electric_guitar");
    else if (button == &pianoButton)   requestSplitStems(uploadedFileId, "piano");
    else if (button == &vocalsButton)  requestSplitStems(uploadedFileId, "vocals");
    else if (button == &bassButton)    requestSplitStems(uploadedFileId, "bass");
    else if (button == &drumsButton)   requestSplitStems(uploadedFileId, "drum");
    else if (button == &splitStemsButton)
    {
        if (!selectedFile.existsAsFile())
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::WarningIcon,
                "No File",
                "Please select a file before splitting stems.");
            return;
        }

        // Show "please wait" message
        progressWindow = std::make_unique<juce::AlertWindow>(
            "Uploading...",
            "Uploading audio file, please wait...",
            juce::AlertWindow::InfoIcon);
        progressWindow->addButton("Cancel", 0);
        progressWindow->enterModalState();

        startSplitStemProcess(selectedFile);
        // Only upload, do not split yet
        // After upload, enable stem buttons (see below)
    }
}

void SplitSteamPageComponent::resized()
{
    auto panelArea = getLocalBounds().reduced(10);

    // Upload area
    auto uploadArea = panelArea.removeFromTop(110).reduced(20, 10);

   uploadTitleLabel.setBounds(uploadArea.getX() + 10, uploadArea.getY() + 10, 200, 28);
    uploadSubtitleLabel.setBounds(uploadArea.getX() + 10, uploadArea.getY() + 40, 320, 22);

    // Set uploadIconButton to a smaller square and align it to the right corner of uploadArea
    int iconButtonSize = juce::jmin(uploadArea.getHeight(), 75); // 40px or less if area is small
    int iconButtonX = uploadArea.getRight() - iconButtonSize - 10; // 10px margin from right
    int iconButtonY = uploadArea.getY() + (uploadArea.getHeight() - iconButtonSize + 8) / 2;
    uploadIconButton.setBounds(iconButtonX, iconButtonY, iconButtonSize, iconButtonSize);


    // Move icon buttons and splitStemsButton a little higher (reduce gridTop and marginBelowIcons)
    int buttonsPerRow = 3;
    int iconButtonHeight = 60;
    int iconButtonMargin = 16;
    int iconButtonWidth = (uploadArea.getWidth() - iconButtonMargin * (buttonsPerRow - 1)) / buttonsPerRow;
    int gridStartX = uploadArea.getX();
    int gridTop = uploadArea.getBottom() + 8; // was 20, now 8

    // First row
    guitarButton.setBounds(gridStartX, gridTop, iconButtonWidth, iconButtonHeight);
    pianoButton.setBounds(gridStartX + iconButtonWidth + iconButtonMargin, gridTop, iconButtonWidth, iconButtonHeight);
    vocalsButton.setBounds(gridStartX + 2 * (iconButtonWidth + iconButtonMargin), gridTop, iconButtonWidth, iconButtonHeight);

    // Second row
    int secondRowTop = gridTop + iconButtonHeight + iconButtonMargin;
    bassButton.setBounds(gridStartX, secondRowTop, iconButtonWidth, iconButtonHeight);
    drumsButton.setBounds(gridStartX + iconButtonWidth + iconButtonMargin, secondRowTop, iconButtonWidth, iconButtonHeight);
    otherButton.setBounds(gridStartX + 2 * (iconButtonWidth + iconButtonMargin), secondRowTop, iconButtonWidth, iconButtonHeight);

    // Reduce margin below icon buttons before Split Stems button
    int marginBelowIcons = 16; // was 30, now 16
    int splitButtonTop = secondRowTop + iconButtonHeight + marginBelowIcons;

    // Split Stems button
    int buttonWidth = uploadArea.getWidth();
    int buttonHeight = 50;
    int maxSplitButtonTop = panelArea.getBottom() - buttonHeight - 10;
    if (splitButtonTop > maxSplitButtonTop)
        splitButtonTop = maxSplitButtonTop;

    splitStemsButton.setBounds(
        uploadArea.getX(),
        splitButtonTop,
        buttonWidth, buttonHeight);
}

void SplitSteamPageComponent::paint(juce::Graphics &g)
{
    auto panelArea = getLocalBounds().reduced(20);
    g.setColour(juce::Colour(0xFFd6d7d9)); // #d6d7d9
    g.fillRoundedRectangle(panelArea.toFloat(), 18.0f);

    auto uploadBox = panelArea.withHeight(100).reduced(10, 10);
    float borderRadius = 10.0f;
    float dashLengths[] = {6.0f, 6.0f};
    juce::Path borderPath;
    borderPath.addRoundedRectangle(uploadBox.toFloat(), borderRadius);

    juce::Path dashedPath;
    juce::PathStrokeType strokeType(2.0f);
    strokeType.createDashedStroke(dashedPath, borderPath, dashLengths, 2);

    // Set the dotted border to black
    g.setColour(juce::Colour(0xFFB0B0B0));
    g.strokePath(dashedPath, strokeType);

    auto buttonBounds = splitStemsButton.getBounds();
    if (!buttonBounds.isEmpty())
    {
        g.setColour(juce::Colour(0xFF424752));
        g.drawRoundedRectangle(buttonBounds.toFloat().reduced(1.0f), 10.0f, 2.0f);
    }
}

void SplitSteamPageComponent::setupIconButton(CustomDrawableButton& button, juce::Drawable* icon, const juce::String& tooltip)
{
    button.setIcon(std::unique_ptr<juce::Drawable>(icon->createCopy()));
    button.setTooltip(tooltip);
    // Set button background color to match upload panel (#d6d7d9)
    button.setColour(juce::TextButton::buttonColourId, juce::Colour(0xFFd6d7d9));
    button.setColour(juce::TextButton::textColourOffId, juce::Colour(0xFF424752));
    button.addListener(this);
    addAndMakeVisible(button);
}

// --- CustomDrawableButton implementation ---

CustomDrawableButton::CustomDrawableButton(const juce::String& name)
    : juce::TextButton(name)
{
}

void CustomDrawableButton::setIcon(std::unique_ptr<juce::Drawable> iconToUse)
{
    icon = std::move(iconToUse);
}

juce::Font CustomDrawableButton::getFontToUse() const
{
    return juce::Font(18.0f, juce::Font::bold);
}

void CustomDrawableButton::paintButton(juce::Graphics& g, bool isMouseOver, bool isButtonDown)
{
    auto bounds = getLocalBounds();

    // Draw button background
    auto bgColour = findColour(juce::TextButton::buttonColourId);
    if (isButtonDown)
        bgColour = bgColour.darker(0.1f);
    else if (isMouseOver)
        bgColour = bgColour.brighter(0.07f);

    g.setColour(bgColour);
    g.fillRoundedRectangle(bounds.toFloat(), 8.0f);

    // Draw border in light grey
    g.setColour(juce::Colour(0xFFB0B0B0));// #B0B0B0
    g.drawRoundedRectangle(bounds.toFloat().reduced(1.0f), 8.0f, 1.0f);

    // Center icon and text as a group
    int iconSize = juce::jmin(bounds.getHeight() - 10, 28);
    if (getButtonText() == "Split Stems")
        iconSize = juce::jmin(bounds.getHeight() - 18, 18); // smaller icon for Split Stems

    int gap = 8;
    g.setFont(getFontToUse());
    int textWidth = g.getCurrentFont().getStringWidth(getButtonText());
    int totalWidth = icon ? (iconSize + gap + textWidth) : textWidth;

    int startX = bounds.getX() + (bounds.getWidth() - totalWidth) / 2;
    int iconX = startX;
    int iconY = bounds.getCentreY() - iconSize / 2;
    int textX = icon ? (iconX + iconSize + gap) : startX;

    // Draw icon (centered vertically, no drop shadow)
    if (icon)
    {
        icon->drawWithin(g, juce::Rectangle<float>((float)iconX, (float)iconY, (float)iconSize, (float)iconSize),
                         juce::RectanglePlacement::centred, 1.0f);
    }

    // Draw text (centered with icon)
    g.setColour(findColour(juce::TextButton::textColourOffId));
    g.drawText(getButtonText(), textX, bounds.getY(),
               bounds.getWidth() - (textX - bounds.getX()), bounds.getHeight(),
               juce::Justification::centredLeft, true);
}



void SplitSteamPageComponent::startSplitStemProcess(const juce::File &file)
{
    // busy = true;

    std::thread([file, this]()
    {
        try
        {
            // Step 1: Upload file to LALAL.AI
            juce::MemoryBlock fileData;
            {
                juce::FileInputStream fis(file);
                if (!fis.openedOk())
                    throw std::runtime_error("Failed to open file for upload");
                fis.readIntoMemoryBlock(fileData);
            }
            // juce::MessageManager::callAsync([] {
            //     juce::AlertWindow::showMessageBoxAsync(
            //         juce::AlertWindow::InfoIcon,
            //         "Upload Response",
            //         "Upload Response");
            // });
                juce::String uploadUrl = "https://www.lalal.ai/api/upload/";
                juce::String contentDisposition = "Content-Disposition: attachment; filename=" + file.getFileName() + "\n";
                juce::String authHeader = "Authorization: license " + juce::String(apiKey) + "\n";

                auto uploadOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inAddress)
                    .withExtraHeaders(contentDisposition + authHeader)
                    .withHttpRequestCmd("POST");

                auto uploadStream = juce::URL(uploadUrl)
                    .withPOSTData(fileData)
                    .createInputStream(uploadOptions);

                if (!uploadStream)
                    throw std::runtime_error("Failed to connect to LALAL.AI API");

                juce::String uploadResponse = uploadStream->readEntireStreamAsString();
                juce::var uploadJson = juce::JSON::parse(uploadResponse);

            // juce::MessageManager::callAsync([uploadResponse] {
            //     juce::AlertWindow::showMessageBoxAsync(
            //         juce::AlertWindow::InfoIcon,
            //         "Upload Response",
            //         uploadResponse);
            // });

            if (!uploadJson.isObject())
                throw std::runtime_error("Invalid JSON from LALAL.AI API");

            auto* uploadObj = uploadJson.getDynamicObject();

            status = uploadObj->getProperty("status").toString();

            if (status != "success")
            {
                juce::String errorMsg = uploadObj->getProperty("error").toString();
                throw std::runtime_error(("LALAL.AI upload error: " + errorMsg).toStdString());
            }
            uploadedFileId = uploadObj->getProperty("id").toString();
            if (uploadedFileId.isEmpty())
                throw std::runtime_error("No file id in LALAL.AI response");

            juce::MessageManager::callAsync([this]()
            {
                if (progressWindow)
                    progressWindow.reset();
                guitarButton.setEnabled(true);
                pianoButton.setEnabled(true);
                vocalsButton.setEnabled(true);
                bassButton.setEnabled(true);
                drumsButton.setEnabled(true);
                otherButton.setEnabled(true);
            });

            // Step 2: Continue with your processing using fileId
            // requestSplitStems(fileId, stem);
        }
        catch (const std::exception& e)
        {
            // busy = false;
            juce::String msg = "LALAL.AI Error: ";
            msg += e.what();
            juce::MessageManager::callAsync([msg]()
            {
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::WarningIcon,
                    "LALAL.AI Error",
                    msg);
            });
        }
    }).detach();
}



void SplitSteamPageComponent::requestSplitStems(const juce::String &uploadedFileId, const juce::String& stem)
{
    std::thread([this, uploadedFileId, stem]()
    {
        juce::URL splitUrl("https://www.lalal.ai/api/split/");
        juce::String paramsJson = "[{\"id\":\"" + uploadedFileId + "\",\"filter\":2,\"stem\":\"" + stem + "\"}]";
        juce::String postData = "params=" + paramsJson;

        auto splitOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inPostData)
            .withExtraHeaders("Authorization: license " + juce::String(apiKey) + "\n"
                              "Content-Type: application/x-www-form-urlencoded\n")
            .withHttpRequestCmd("POST");

        std::unique_ptr<juce::InputStream> splitStream = splitUrl.withPOSTData(postData).createInputStream(splitOptions);

        if (!splitStream)
        {
            // busy = false;
            juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Error",
                    "Failed to start split job.");
            });
            return;
        }

        juce::String splitResponse = splitStream->readEntireStreamAsString();
        juce::var splitJson = juce::JSON::parse(splitResponse);

        // juce::MessageManager::callAsync([splitResponse] {
        //     juce::AlertWindow::showMessageBoxAsync(
        //         juce::AlertWindow::InfoIcon,
        //         "Split Response",
        //         splitResponse);
        // });

        if (splitJson.isArray() && splitJson.size() > 0)
        {
            auto* splitObj = splitJson[0].getDynamicObject();
            juce::String taskId = splitObj->getProperty("task_id").toString();
            if (taskId.isEmpty())
            {
                // busy = false;
                juce::MessageManager::callAsync([] {
                    juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                        "Error",
                        "No job id in split response.");
                });
                return;
            }
            pollForResult(taskId, uploadedFileId, stem);
        }
        else if (splitJson.isObject())
        {
            auto* splitObj = splitJson.getDynamicObject();
            juce::String taskId = splitObj->getProperty("task_id").toString();
            if (taskId.isNotEmpty())
            {
                pollForResult(taskId, uploadedFileId, stem);
                return;
            }
            juce::String errorMsg = splitObj->getProperty("error").toString();
            // busy = false;
            juce::MessageManager::callAsync([errorMsg] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Split Error",
                    errorMsg.isNotEmpty() ? errorMsg : "Invalid split response from LALAL.AI.");
            });
            return;
        }
        else
        {
            // busy = false;
            juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Error",
                    "Invalid split response from LALAL.AI.");
            });
            return;
        }
    }).detach();
}

void SplitSteamPageComponent::pollForResult(const juce::String& taskId, const juce::String& uploadedFileId, const juce::String& stem)
{
    std::thread([this, taskId, uploadedFileId, stem]()
    {
        const juce::String pollUrlBase = "https://www.lalal.ai/api/check/";
        while (true)
        {
                juce::URL pollUrl(pollUrlBase);
                auto pollOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inPostData)
                    .withExtraHeaders("Authorization: license " + juce::String(apiKey) + "\n"
                                      "Content-Type: application/x-www-form-urlencoded\n")
                    .withHttpRequestCmd("POST");

                juce::String postData = "id=" + juce::URL::addEscapeChars(uploadedFileId, true);
                auto pollStream = pollUrl.withPOSTData(postData).createInputStream(pollOptions);

                juce::String pollResponse = pollStream->readEntireStreamAsString();
                juce::var pollJson = juce::JSON::parse(pollResponse);

                auto* pollObj = pollJson.getDynamicObject();
                juce::String status = pollObj->getProperty("status").toString();
                auto resultVar = pollObj->getProperty("result");
                auto* resultObj = resultVar.getDynamicObject();

                auto fileResultVar = resultObj->getProperty(uploadedFileId);


                auto* fileResultObj = fileResultVar.getDynamicObject();
                juce::String fileStatus = fileResultObj->getProperty("status").toString();


                auto taskVar = fileResultObj->getProperty("task");
                auto* taskObj = taskVar.getDynamicObject();
                juce::String taskState = taskObj->getProperty("state").toString();

                

                if(fileStatus == "success" && taskState == "success")
                {
                juce::String downloadUrl = "https://d.lalal.ai/media/split/" + uploadedFileId + "/" + taskId + "/" + juce::String(stem);
               
                // juce::MessageManager::callAsync([downloadUrl] {
                // juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                //     "downloadUrl",
                //     downloadUrl);
                // });
                // juce::String downloadUrl = "https://d.lalal.ai/media/split/6af00a36-9ce2-4da0-a102-e59bc945ba10/2a10bd10-5517-4703-ab82-3a48d527e049/vocals";
        
               juce::File outDir = juce::File::getSpecialLocation(juce::File::userDocumentsDirectory).getChildFile("AIpluginStatic");
                outDir.createDirectory(); // Ensure the directory exists

                juce::String originalName = selectedFile.getFileNameWithoutExtension();
                juce::String extension = selectedFile.getFileExtension();
                juce::String stemName = stem; // e.g., "vocals", "guitar", etc.
                juce::String outFileName = originalName + "_" + stemName;
                juce::File outFile = outDir.getNonexistentChildFile(outFileName, extension);
                downloadProcessedFile(downloadUrl, outFile.getFullPathName());
                break;
        
                }else if(fileStatus == "error")
                {
                    juce::String errorMsg = fileResultObj->getProperty("error").toString();
                    juce::MessageManager::callAsync([errorMsg]()
                    {
                        juce::AlertWindow::showMessageBoxAsync(
                            juce::AlertWindow::WarningIcon,
                            "Error",
                            "LALAL.AI returned an error: " + errorMsg);
                    });
                }
                else {
                    // auto taskVar = fileResultObj->getProperty("task");
                    if (taskVar.isObject())
                    {
                      
                        if (taskState == "progress")
                        {
                            int progress = (int)taskObj->getProperty("progress");
                            juce::MessageManager::callAsync([this, progress]()
                            {
                                if (!progressWindow)
                                {
                                    progressWindow = std::make_unique<juce::AlertWindow>(
                                        "Progress",
                                        "Processing... " + juce::String(progress) + "%",
                                        juce::AlertWindow::InfoIcon);
                                    progressWindow->addButton("Cancel", 0);
                                    progressWindow->enterModalState();
                                }
                                else
                                {
                                    progressWindow->setMessage("Processing... " + juce::String(progress) + "%");
                                    progressWindow->repaint();
                                }
                            });
                            std::this_thread::sleep_for(std::chrono::seconds(1));
                            continue;
                        }
                        else if (taskState == "error")
                        {
                            juce::String errorMsg = taskObj->getProperty("error").toString();
                            juce::MessageManager::callAsync([errorMsg]()
                                {
                                    juce::AlertWindow::showMessageBoxAsync(
                                        juce::AlertWindow::WarningIcon,
                                        "Error",
                                        "LALAL.AI task error: " + errorMsg);
                                });
                            // busy = false;
                            return;
                        }
                    }
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
        }
        // busy = false;
    }).detach();
}

void SplitSteamPageComponent::downloadProcessedFile(const juce::String& downloadUrl, const juce::String& outputPath)
{
    try
    {
        // Set up browser-like headers for the download request
        juce::String headers = 
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\n"
            "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n"
            "Accept-Language: en-US,en;q=0.9\n"
            "Connection: keep-alive\n"
            "Sec-Fetch-Dest: document\n"
            "Sec-Fetch-Mode: navigate\n"
            "Sec-Fetch-Site: none\n"
            "Sec-Fetch-User: ?1\n"
            "Upgrade-Insecure-Requests: 1\n";

        auto options = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inAddress)
            .withExtraHeaders(headers)
            .withConnectionTimeoutMs(30000)  // 30 second timeout
            .withStatusCode(nullptr);

        // Create the download stream
        std::unique_ptr<juce::InputStream> downloadStream = juce::URL(downloadUrl).createInputStream(options);
        
        if (!downloadStream)
        {
            throw std::runtime_error("Failed to create download stream");
        }

        // Create output file
        juce::File outputFile(outputPath);
        if (outputFile.existsAsFile())
            outputFile.deleteFile();

        std::unique_ptr<juce::FileOutputStream> fileStream(outputFile.createOutputStream());
        if (!fileStream)
        {
            throw std::runtime_error("Failed to create output file");
        }

        // Read the first few bytes to check content type (to detect HTML error pages)
        juce::MemoryBlock header(64);
        downloadStream->read(header.getData(), 64);
        juce::String headerStr(static_cast<const char*>(header.getData()), 64);
        
        if (headerStr.containsIgnoreCase("<!DOCTYPE html>") || headerStr.containsIgnoreCase("<html"))
        {
            throw std::runtime_error("Received HTML instead of audio file - download failed");
        }

        // Write the header we read
        fileStream->write(header.getData(), header.getSize());

        // Copy the rest of the data
        constexpr int bufferSize = 8192;
        juce::HeapBlock<char> buffer(bufferSize);
        
        while (!downloadStream->isExhausted())
        {
            auto bytesRead = downloadStream->read(buffer, bufferSize);
            if (bytesRead < 0)
                break;
            
            if (bytesRead > 0)
                fileStream->write(buffer, static_cast<size_t>(bytesRead));
        }

        fileStream->flush();

         juce::MessageManager::callAsync([this, outputFile]()
        {
            if (progressWindow)
                progressWindow.reset();

            // Play the downloaded file automatically
           if (playerBarComponent)
                 playerBarComponent->loadAndPlayFile(outputFile);

            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::InfoIcon,
                "Download Complete",
                "File saved to:\n" + outputFile.getFullPathName());
        });
    }
    catch (const std::exception& e)
    {
        juce::String errorMsg = "Download failed: ";
        errorMsg += e.what();
        juce::MessageManager::callAsync([errorMsg]()
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::WarningIcon,
                "Download Error",
                errorMsg);
        });
    }
}



