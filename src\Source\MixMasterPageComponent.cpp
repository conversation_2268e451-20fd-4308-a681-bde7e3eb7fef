#include "MixMasterPageComponent.h"
#include "BinaryData.h"

MixMasterPageComponent::MixMasterPageComponent()
{
    uploadButton.setButtonText("Upload a track");
    uploadButton.setColour(juce::TextButton::buttonColourId, juce::Colours::lightgrey);
    uploadButton.setColour(juce::TextButton::textColourOnId, juce::Colours::black);
    uploadButton.setColour(juce::TextButton::textColourOffId, juce::Colours::black);
    addAndMakeVisible(uploadButton);

    icon_mix_upload = juce::Drawable::createFromImageData(BinaryData::icon_mix_upload_png, BinaryData::icon_mix_upload_pngSize);
}

MixMasterPageComponent::~MixMasterPageComponent() {}

void MixMasterPageComponent::paint(juce::Graphics &g)
{
    juce::Path dashedCircle;
    auto radius = 170.0f;
    auto center = getLocalBounds().getCentre().toFloat();
    dashedCircle.addEllipse(center.x - radius, center.y - radius, radius * 2, radius * 2);

    float dashLengths[] = {12.0f, 12.0f};
    juce::Path dashedPath;
    juce::PathStrokeType(3.0f).createDashedStroke(dashedPath, dashedCircle, dashLengths, 2);

    g.setColour(juce::Colours::mediumpurple);
    g.strokePath(dashedPath, juce::PathStrokeType(3.0f));

    if (icon_mix_upload)
        icon_mix_upload->drawWithin(g, juce::Rectangle<float>(center.x - 30, center.y - 100, 60, 60), juce::RectanglePlacement::centred, 1.0f);

    g.setColour(juce::Colours::black);
    g.setFont(juce::Font(20.0f, juce::Font::bold));

    int textYOffset = 55;
    int textHeight = 90;
    int textPadding = 0;

    auto textRect = juce::Rectangle<int>(
        static_cast<int>(center.x - radius + textPadding),
        static_cast<int>(center.y + textYOffset),
        static_cast<int>(radius * 2 - 2 * textPadding),
        textHeight);

    auto firstLineRect = textRect.removeFromTop(textRect.getHeight() / 2);
    auto secondLineRect = textRect;

    g.drawText("Accepted file formats:", firstLineRect, juce::Justification::centred);
    g.drawText("MP3, M4A, WAV, AAC, OGG", secondLineRect, juce::Justification::centred);
}

void MixMasterPageComponent::resized()
{
    auto area = getLocalBounds();
    auto center = area.getCentre();
    int buttonWidth = 120;
    int buttonHeight = 48;
    int circleRadius = 150;

    int iconSize = 60;
    int iconY = center.y - 100;

    juce::Font font(20.0f, juce::Font::bold);
    juce::String firstLine = "Accepted file formats:";
    int textWidth = static_cast<int>(font.getStringWidthFloat(firstLine));
    buttonWidth = textWidth + 32;

    int buttonY = iconY + iconSize + 16;

    uploadButton.setBounds(
        center.x - buttonWidth / 2,
        buttonY,
        buttonWidth,
        buttonHeight);
}