#include "LibraryPageComponent.h"
#include "BinaryData.h"

LibraryPageComponent::LibraryPageComponent()
{
    image = juce::ImageCache::getFromMemory(BinaryData::image_png, BinaryData::image_pngSize);
    iconFavourite = juce::ImageCache::getFromMemory(BinaryData::icon_favourite_png, BinaryData::icon_favourite_pngSize);
    if (image.isNull())
        DBG("Failed to load image.png!");
    if (iconFavourite.isNull())
        DBG("Failed to load icon_favourite.png!");
}

LibraryPageComponent::~LibraryPageComponent() = default;

void LibraryPageComponent::paint(juce::Graphics &g)
{
    const int numRows = 3;
    const int numCols = 2;
    const int cardMargin = 24;
    const int cardCorner = 12;
    auto area = getLocalBounds().reduced(40);

    int cardWidth = (area.getWidth() - cardMargin) / numCols;
    int cardHeight = (area.getHeight() - 2 * cardMargin) / numRows;

    struct CardInfo
    {
        juce::String title, subtitle, likes;
        juce::Image image;
    };
    std::vector<CardInfo> cards = {
        {"IShowSpeed", "SomeDud3", "53.4k", image},
        {"Card 2", "Artist 2", "12.1k", image},
        {"Card 3", "Artist 3", "8.7k", image},
        {"Card 4", "Artist 4", "21.3k", image},
        {"Card 5", "Artist 5", "5.2k", image},
        {"Card 6", "Artist 6", "1.9k", image}};

    int cardIdx = 0;
    for (int row = 0; row < numRows; ++row)
    {
        for (int col = 0; col < numCols; ++col)
        {
            if (cardIdx >= cards.size())
                break;

            auto cardX = area.getX() + col * (cardWidth + cardMargin);
            auto cardY = area.getY() + row * (cardHeight + cardMargin);
            juce::Rectangle<float> cardBounds((float)cardX, (float)cardY, (float)cardWidth, (float)cardHeight);

            // Card background
            g.setColour(juce::Colours::black.withAlpha(0.05f));
            g.fillRoundedRectangle(cardBounds, cardCorner);

            // Draw the image cropped to rounded rectangle
            if (!cards[cardIdx].image.isNull())
            {
                juce::Rectangle<float> imageBounds = cardBounds.reduced(5.0f, cardHeight * 0.25f);
                juce::Path clipPath;
                clipPath.addRoundedRectangle(imageBounds, cardCorner - 4.0f);
                g.saveState();
                g.reduceClipRegion(clipPath);
                g.drawImage(cards[cardIdx].image, imageBounds.toNearestInt().toFloat());
                g.restoreState();
            }

            // Overlay gradient for text readability
            g.setGradientFill(juce::ColourGradient(juce::Colours::black.withAlpha(0.6f),
                                                   0, cardBounds.getBottom() - 60,
                                                   juce::Colours::transparentBlack,
                                                   0, cardBounds.getBottom(), false));
            g.fillRoundedRectangle(cardBounds.withY(cardBounds.getBottom() - 60).withHeight(60), cardCorner);

            // Draw text
            g.setColour(juce::Colours::white);
            g.setFont(18.0f);
            g.drawText(cards[cardIdx].title, cardBounds.withTrimmedTop(cardHeight - 54).withHeight(28), juce::Justification::centredLeft);

            g.setFont(14.0f);
            g.drawText(cards[cardIdx].subtitle, cardBounds.withTrimmedTop(cardHeight - 28).withHeight(18), juce::Justification::centredLeft);

            // Draw heart icon and likes count together, right-aligned
            float iconSize = 18.0f;
            auto likesArea = cardBounds.withTrimmedTop(cardHeight - 54).withHeight(28);

            if (!iconFavourite.isNull())
            {
                juce::Rectangle<float> iconBounds(
                    likesArea.getRight() - iconSize - 6, // 6px padding from right
                    likesArea.getY() + (likesArea.getHeight() - iconSize) / 2,
                    iconSize, iconSize);
                g.drawImage(iconFavourite, iconBounds);
            }

            g.setFont(16.0f);
            g.drawText(cards[cardIdx].likes,
                       likesArea.withRight(likesArea.getRight() - iconSize - 10),
                       juce::Justification::centredRight);

            ++cardIdx;
        }
    }
}

void LibraryPageComponent::resized()
{
    // Add layout logic for child components if necessary
}