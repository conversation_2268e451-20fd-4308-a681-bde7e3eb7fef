// This is a JUCE-based C++ class that mirrors your Python script functionality.
// It downloads segments from Lalal.ai and merges them into a single audio file.

#include <JuceHeader.h>
#include <regex>

class AudioSegmentMerger : public juce::Thread
{
public:
    AudioSegmentMerger(const juce::String& idToUse = {}, bool vocals = true)
        : juce::Thread("AudioSegmentMerger"), userId(idToUse), include<PERSON><PERSON><PERSON>(vocals) {}

    void run() override
    {
       

        const juce::String fileId = userId.isNotEmpty() ? extractFileId(userId) :
        extractFileId(getUserInput("Enter the URL of the segment or ID: "));
          juce::MessageManager::callAsync([fileId] {
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::InfoIcon,
                    "LALAL.AI",
                    fileId);
            }); 
        if (fileId.isEmpty()) {
             juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::InfoIcon,
                    "LALAL.AI",
                    "Invalid ID or URL.");
            });
            // DBG("Invalid ID or URL.");
            return;
        }

        const juce::String baseUrl = getBaseUrl(fileId, includeVocals);
        juce::Array<juce::File> segmentFiles;
        int segmentNumber = 0;

        while (!threadShouldExit()) {
            const auto file = downloadSegment(baseUrl, segmentNumber);
            if (file.existsAsFile()) {
                segmentFiles.add(file);
                ++segmentNumber;
            }
            else {
                break;
            }
        }
         juce::MessageManager::callAsync([fileId,baseUrl ] {
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::InfoIcon,
                    "file Id",
                    "File Id:"+ fileId + "\nBase URL: " + baseUrl);
            });
        if (!segmentFiles.isEmpty()) {
            mergeSegments(segmentFiles);
        }
        else {
              juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::InfoIcon,
                    "LALAL.AI",
                    "No segments downloaded.");
            });
            // DBG("No segments downloaded.");
        }
    }

private:
    juce::String userId;
    bool includeVocals;
    const juce::File downloadDir = juce::File::getSpecialLocation(juce::File::tempDirectory).getChildFile("lalal_segments");
    const juce::File mergedFile = juce::File::getSpecialLocation(juce::File::userDesktopDirectory).getChildFile("merged_audio.wav");

    juce::String getUserInput(const juce::String& prompt)
    {
        std::cout << prompt << std::endl;
        std::string input;
        std::getline(std::cin, input);
        return juce::String(input);
    }

    juce::String extractFileId(const juce::String& inputStr)
    {
        std::regex idPattern("^[a-f0-9\-/]{36,}$");
        if (std::regex_match(inputStr.toStdString(), idPattern)) {
            return inputStr;
        }

        std::regex urlPattern("/api/split/(.*?/.*?)/");
        std::smatch match;
        std::string str = inputStr.toStdString();
        if (std::regex_search(str, match, urlPattern)) {
            return match[1].str();
        }

        return "";
    }

    juce::String getBaseUrl(const juce::String& fileId, bool vocals)
    {
        const juce::String vocalsPath = vocals ? "vocals_playlist" : "no_vocals_playlist";
        return "https://www.lalal.ai/api/split/" + fileId + "/" + vocalsPath + "/segment-";
    }

    juce::File downloadSegment(const juce::String& baseUrl, int segmentNumber)
    {
        static const std::vector<juce::String> extensions = { ".wav", ".mp3", ".flac", ".aac", ".ogg" };
        downloadDir.createDirectory();

        for (const auto& ext : extensions)
        {
            const auto url = baseUrl + juce::String::formatted("%03d", segmentNumber) + ext;
            const auto filePath = downloadDir.getChildFile("segment-" + juce::String::formatted("%03d", segmentNumber) + ext);

            juce::URL juceUrl(url);
            std::unique_ptr<juce::InputStream> stream(juceUrl.createInputStream(false));

            if (stream != nullptr && stream->getTotalLength() > 0)
            {
                filePath.deleteFile();
                juce::FileOutputStream outStream(filePath);
                outStream.writeFromInputStream(*stream, -1);
                DBG("Downloaded: " << filePath.getFullPathName());
                return filePath;
            }
        }

        DBG("Segment not found in any supported format: segment-" + juce::String::formatted("%03d", segmentNumber));
        return juce::File();
    }

    void mergeSegments(const juce::Array<juce::File>& segmentFiles)
    {
        juce::AudioFormatManager formatManager;
        formatManager.registerBasicFormats();
        juce::AudioSampleBuffer mergedBuffer;

        int totalSamples = 0;
        int numChannels = 0;

        for (const auto& file : segmentFiles)
        {
            std::unique_ptr<juce::AudioFormatReader> reader(formatManager.createReaderFor(file));
            if (reader != nullptr)
            {
                juce::AudioSampleBuffer tempBuffer((int)reader->numChannels, (int)reader->lengthInSamples);
                reader->read(&tempBuffer, 0, (int)reader->lengthInSamples, 0, true, true);

                if (mergedBuffer.getNumSamples() == 0)
                {
                    numChannels = tempBuffer.getNumChannels();
                    mergedBuffer.setSize(numChannels, tempBuffer.getNumSamples());
                    mergedBuffer.makeCopyOf(tempBuffer);
                }
                else
                {
                    int oldSamples = mergedBuffer.getNumSamples();
                    mergedBuffer.setSize(numChannels, oldSamples + tempBuffer.getNumSamples(), true, true, true);
                    for (int ch = 0; ch < numChannels; ++ch)
                        mergedBuffer.copyFrom(ch, oldSamples, tempBuffer, ch, 0, tempBuffer.getNumSamples());
                }
            }
        }

        juce::WavAudioFormat wavFormat;
        std::unique_ptr<juce::FileOutputStream> outputStream(mergedFile.createOutputStream());
        if (outputStream != nullptr)
        {
            std::unique_ptr<juce::AudioFormatWriter> writer(wavFormat.createWriterFor(outputStream.get(), 44100.0, mergedBuffer.getNumChannels(), 16, {}, 0));
            outputStream.release();
            if (writer != nullptr)
            {
                writer->writeFromAudioSampleBuffer(mergedBuffer, 0, mergedBuffer.getNumSamples());
                DBG("Merged audio saved as: " << mergedFile.getFullPathName());
            }
        }
    }
};

// Example call after polling result is ready and job ID is retrieved:
// auto* merger = new AudioSegmentMerger("your-job-id-here", true);
// merger->startThread();
