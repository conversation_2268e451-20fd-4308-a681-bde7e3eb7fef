#include "KeyBpmFinderComponent.h"
#include "BinaryData.h"
#include <cstdint> // Add this at the top if not already present
extern "C" {
    #include <aubio/aubio.h>
}

// CenteredHeaderLookAndFeel
void CenteredHeaderLookAndFeel::drawTableHeaderColumn(juce::Graphics& g, juce::TableHeaderComponent& header,
    const juce::String& columnName, int /*columnId*/, int width, int height, bool isMouseOver, bool isButtonDown, int /*columnFlags*/)
{
    auto background = header.findColour(juce::TableHeaderComponent::backgroundColourId);
    g.setColour(background);
    g.fillRect(0, 0, width, height);

    if (isButtonDown || isMouseOver)
        g.setColour(header.findColour(juce::TableHeaderComponent::highlightColourId).withAlpha(isButtonDown ? 0.2f : 0.09f));
    else
        g.setColour(background);

    g.fillRect(0, 0, width, height);

    g.setColour(header.findColour(juce::TableHeaderComponent::textColourId));
    g.setFont(height * 0.6f);

    // Centered text
    g.drawFittedText(columnName, 0, 0, width, height, juce::Justification::centred, 1);
}

void DropAreaComponent::paint(juce::Graphics& g)
{
    constexpr int margin = 8;
    constexpr int padding = 16;
    constexpr float borderRadius = 16.0f;

    auto outerBounds = getLocalBounds().reduced(margin);

    // Draw outer rounded border
    g.setColour(juce::Colours::darkgrey);
    g.drawRoundedRectangle(outerBounds.toFloat(), borderRadius, 2.0f);

    // Fill background only inside the outer border
    g.setColour(juce::Colours::lightgrey);
    g.fillRoundedRectangle(outerBounds.toFloat(), borderRadius);

    // Inner bounds with padding for content and dotted border
    auto innerBounds = outerBounds.reduced(padding);

    // Dotted border (inner)
    juce::Path border;
    border.addRoundedRectangle(innerBounds.toFloat(), borderRadius - 4.0f);
    float dashLengths[] = {4.0f, 4.0f};
    juce::Path dashedBorder;
    juce::PathStrokeType strokeType(2.0f);
    strokeType.createDashedStroke(dashedBorder, border, dashLengths, 2);

    g.setColour(juce::Colours::darkgrey.withAlpha(0.7f));
    g.strokePath(dashedBorder, strokeType);

    // Draw icon centered at top of inner area
    int iconSize = juce::jmin(innerBounds.getWidth() / 4, 64);
    int iconX = innerBounds.getCentreX() - iconSize / 2;
    int iconY = innerBounds.getY() + 12;
    if (!icon.isNull())
        g.drawImageWithin(icon, iconX, iconY, iconSize, iconSize, juce::RectanglePlacement::centred);

    // Draw text below icon
    g.setColour(juce::Colours::darkgrey);
    g.setFont(16.0f);
    juce::String text = "Browse or drag & drop audio files here to analyze your music.";
    int textY = iconY + iconSize + 12;
    g.drawFittedText(text, innerBounds.withY(textY).withHeight(innerBounds.getBottom() - textY), juce::Justification::centredTop, 3);
}

// Only trigger onClicked if the icon is clicked
void DropAreaComponent::mouseUp(const juce::MouseEvent& event)
{
    constexpr int margin = 8;
    constexpr int padding = 16;
    auto outerBounds = getLocalBounds().reduced(margin);
    auto innerBounds = outerBounds.reduced(padding);

    int iconSize = juce::jmin(innerBounds.getWidth() / 4, 64);
    int iconX = innerBounds.getCentreX() - iconSize / 2;
    int iconY = innerBounds.getY() + 12;
    juce::Rectangle<int> iconRect(iconX, iconY, iconSize, iconSize);

    if (iconRect.contains(event.getPosition()))
    {
        if (onClicked)
        {
            juce::MessageManager::callAsync([this] { onClicked(); });
        }
    }
}

// SeparatorLine
void KeyBpmFinderComponent::SeparatorLine::paint(juce::Graphics& g)
{
    g.setColour(juce::Colours::grey.withAlpha(0.5f));
    g.fillRect(getLocalBounds().withHeight(2));
}

// KeyBpmFinderComponent
KeyBpmFinderComponent::KeyBpmFinderComponent()
    : dropArea(loadMusicIcon())
{
    addAndMakeVisible(dropArea);

    addAndMakeVisible(separatorLine);
    separatorLine.setInterceptsMouseClicks(false, false);

    addAndMakeVisible(table);
    table.setModel(this);

    // Center header text using custom LookAndFeel
    static CenteredHeaderLookAndFeel centeredHeaderLF;
    table.getHeader().setLookAndFeel(&centeredHeaderLF);

    // Add columns with centered justification
    table.getHeader().addColumn("File Name", 1, 200, 50, 1000, juce::TableHeaderComponent::ColumnPropertyFlags::defaultFlags, juce::Justification::centred);
    table.getHeader().addColumn("Key", 2, 100, 50, 1000, juce::TableHeaderComponent::ColumnPropertyFlags::defaultFlags, juce::Justification::centred);
    table.getHeader().addColumn("Alt Key", 3, 100, 50, 1000, juce::TableHeaderComponent::ColumnPropertyFlags::defaultFlags, juce::Justification::centred);
    table.getHeader().addColumn("BPM", 4, 100, 50, 1000, juce::TableHeaderComponent::ColumnPropertyFlags::defaultFlags, juce::Justification::centred);

    // Connect drop area click to file dialog
    dropArea.onClicked = [this]() { openFileDialogAndProcess(); };
}

void KeyBpmFinderComponent::resized()
{
    auto area = getLocalBounds().reduced(20);

    // Drop area: top 60% of the space
    auto dropAreaBounds = area.removeFromTop(area.getHeight() * 0.6f);
    dropArea.setBounds(dropAreaBounds);

    // Separator: 2px high, 12px vertical margin
    auto sepArea = area.removeFromTop(16);
    separatorLine.setBounds(sepArea.withHeight(2).reduced(0, 7));

    // Table: width matches drop area, height based on number of rows
    int rowHeight = table.getRowHeight();
    int tableHeight = static_cast<int>(fileData.size()) * rowHeight + table.getHeader().getHeight();

    // Table is placed below the separator, same width as drop area
    juce::Rectangle<int> tableBounds = dropAreaBounds.withY(separatorLine.getBottom()).withHeight(tableHeight);
    table.setBounds(tableBounds);

    // Dynamically set each column width to 1/4 of drop area width
    int colWidth = dropAreaBounds.getWidth() / 4;
    for (int col = 0; col < 4; ++col)
        table.getHeader().setColumnWidth(col + 1, colWidth);
}

int KeyBpmFinderComponent::getNumRows()
{
    return static_cast<int>(fileData.size());
}

void KeyBpmFinderComponent::paintRowBackground(juce::Graphics& g, int rowNumber, int width, int height, bool rowIsSelected)
{
    g.fillAll(rowIsSelected ? juce::Colours::lightblue : juce::Colours::white);
    g.setColour(juce::Colours::grey);
    g.drawRect(0, 0, width, height);
}

void KeyBpmFinderComponent::paintCell(juce::Graphics& g, int rowNumber, int columnId, int width, int height, bool /*rowIsSelected*/)
{
    g.setColour(juce::Colours::black);
    g.setFont(14.0f);

    if (rowNumber < fileData.size())
    {
        const auto& row = fileData[rowNumber];
        juce::String text;
        switch (columnId)
        {
            case 1: text = row.fileName; break;
            case 2: text = row.key; break;
            case 3: text = row.altKey; break;
            case 4: text = row.bpm; break;
        }
        g.drawText(text, 2, 0, width - 4, height, juce::Justification::centred, true);
    }
}

juce::Image KeyBpmFinderComponent::loadMusicIcon()
{
    return juce::ImageCache::getFromMemory(BinaryData::icon_music_png, BinaryData::icon_music_pngSize);
}

void KeyBpmFinderComponent::openFileDialogAndProcess()
{
    chooser = std::make_unique<juce::FileChooser>(
        "Select an audio file...",
        juce::File{},
        "*.wav;*.mp3;*.flac;*.ogg;*.aiff;*.aif",
        false // force embedded dialog
    );

    chooser->launchAsync(
        juce::FileBrowserComponent::openMode | juce::FileBrowserComponent::canSelectFiles,
        [this](const juce::FileChooser& fc)
        {
            auto file = fc.getResult();
            if (file.existsAsFile())
            {
                selectedFile = file;
                // Replace with your processing function:
                // startNoiseRemovalProcess(file);
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::InfoIcon,
                    "Key/Bpm finder",
                    file.getFileName());
                // processFileWithAubio(file);
            }
            chooser.reset(); // Release the dialog after use
        }
    );
}



void KeyBpmFinderComponent::processFileWithAubio(const juce::File& file)
{
    juce::AudioFormatManager formatManager;
    formatManager.registerBasicFormats();
    std::unique_ptr<juce::AudioFormatReader> reader(formatManager.createReaderFor(file));

    if (!reader)
    {
        juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon, "Error", "Could not open audio file.");
        return;
    }

    int64_t numSamples = juce::jmin(reader->lengthInSamples, static_cast<int64_t>(reader->sampleRate * 60)); // limit to 60 seconds

    juce::AudioBuffer<float> buffer((int)reader->numChannels, (int)numSamples);
    reader->read(&buffer, 0, (int)numSamples, 0, true, true);

    // Convert to mono
    juce::AudioBuffer<float> monoBuffer(1, (int)numSamples);
    monoBuffer.clear();
    for (int ch = 0; ch < buffer.getNumChannels(); ++ch)
        monoBuffer.addFrom(0, 0, buffer, ch, 0, (int)numSamples, 1.0f / buffer.getNumChannels());

    // --- Aubio BPM detection ---
    uint_t win_s = 1024;
    uint_t hop_s = 512;
    aubio_tempo_t* tempo = new_aubio_tempo("default", win_s, hop_s, reader->sampleRate);
    fvec_t* in = new_fvec(hop_s);

    int nHops = (int)numSamples / hop_s;
    for (int i = 0; i < nHops; ++i)
    {
        for (uint_t j = 0; j < hop_s; ++j)
        {
            int idx = i * hop_s + j;
            in->data[j] = (idx < numSamples) ? monoBuffer.getSample(0, idx) : 0.0f;
        }
        aubio_tempo_do(tempo, in, nullptr);
    }
    float bpm = aubio_tempo_get_bpm(tempo);

    // --- Aubio Pitch detection for key estimation ---
    aubio_pitch_t* pitchDetector = new_aubio_pitch("default", win_s, hop_s, reader->sampleRate);
    aubio_pitch_set_unit(pitchDetector, "Hz");
    aubio_pitch_set_silence(pitchDetector, -40); // adjust threshold as needed

    fvec_t* pitchIn = new_fvec(hop_s);
    fvec_t* pitchOut = new_fvec(1);

    // Pitch class histogram (12 semitones)
    std::vector<int> pitchClassCounts(12, 0);

    for (int i = 0; i < nHops; ++i)
    {
        for (uint_t j = 0; j < hop_s; ++j)
        {
            int idx = i * hop_s + j;
            pitchIn->data[j] = (idx < numSamples) ? monoBuffer.getSample(0, idx) : 0.0f;
        }
        aubio_pitch_do(pitchDetector, pitchIn, pitchOut);

        float freq = pitchOut->data[0];
        if (freq > 0)
        {
            // Convert frequency to MIDI note number
            int midiNote = (int)(69 + 12 * std::log2(freq / 440.0f) + 0.5f);
            if (midiNote >= 0)
            {
                int pitchClass = midiNote % 12;
                pitchClassCounts[pitchClass]++;
            }
        }
    }

    // Find most frequent pitch class as estimated key root
    int maxIndex = std::distance(pitchClassCounts.begin(), std::max_element(pitchClassCounts.begin(), pitchClassCounts.end()));

    static const char* noteNames[12] = { "C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B" };
    juce::String estimatedKey = noteNames[maxIndex];

    // --- Camelot notation (optional, extend as needed) ---
    juce::String camelot = "-";
    if (estimatedKey == "C") camelot = "8B"; // example mapping
    else if (estimatedKey == "A") camelot = "11A";
    // Add more mappings here...

    // --- Energy calculation ---
    double energySum = 0.0;
    for (int i = 0; i < numSamples; ++i)
    {
        float sample = monoBuffer.getSample(0, i);
        energySum += sample * sample;
    }
    double avgEnergy = energySum / (double)numSamples;

    // Clean up aubio objects
    del_aubio_tempo(tempo);
    del_fvec(in);
    del_aubio_pitch(pitchDetector);
    del_fvec(pitchIn);
    del_fvec(pitchOut);

    // Add result to your fileData table or UI
    fileData.push_back({
        file.getFileName(),
        estimatedKey,
        camelot,
        juce::String(bpm, 1),
        juce::String(avgEnergy, 6) // Add energy
    });
    table.updateContent();
    repaint();
}


