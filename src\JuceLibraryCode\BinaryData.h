/* =========================================================================================

   This is an auto-generated file: Any edits you make may be overwritten!

*/

#pragma once

namespace BinaryData
{
    extern const char*   icon_bass_png;
    const int            icon_bass_pngSize = 8473;

    extern const char*   icon_create_png;
    const int            icon_create_pngSize = 21252;

    extern const char*   icon_drum_png;
    const int            icon_drum_pngSize = 48837;

    extern const char*   icon_favourite_png;
    const int            icon_favourite_pngSize = 3137;

    extern const char*   icon_guitar_png;
    const int            icon_guitar_pngSize = 30599;

    extern const char*   icon_keybpm_png;
    const int            icon_keybpm_pngSize = 5769;

    extern const char*   icon_library_png;
    const int            icon_library_pngSize = 14764;

    extern const char*   icon_menu_png;
    const int            icon_menu_pngSize = 3773;

    extern const char*   icon_micro_png;
    const int            icon_micro_pngSize = 3431;

    extern const char*   icon_mini_music_png;
    const int            icon_mini_music_pngSize = 459;

    extern const char*   icon_mix_png;
    const int            icon_mix_pngSize = 16528;

    extern const char*   icon_mix_upload_png;
    const int            icon_mix_upload_pngSize = 3042;

    extern const char*   icon_music_png;
    const int            icon_music_pngSize = 2301;

    extern const char*   icon_next_png;
    const int            icon_next_pngSize = 868;

    extern const char*   icon_noise_png;
    const int            icon_noise_pngSize = 16114;

    extern const char*   icon_open_png;
    const int            icon_open_pngSize = 9078;

    extern const char*   icon_other_png;
    const int            icon_other_pngSize = 26270;

    extern const char*   icon_pause_png;
    const int            icon_pause_pngSize = 12693;

    extern const char*   icon_piano_png;
    const int            icon_piano_pngSize = 3425;

    extern const char*   icon_play_png;
    const int            icon_play_pngSize = 13691;

    extern const char*   icon_prev_png;
    const int            icon_prev_pngSize = 944;

    extern const char*   icon_split_png;
    const int            icon_split_pngSize = 11989;

    extern const char*   icon_train_png;
    const int            icon_train_pngSize = 17877;

    extern const char*   icon_upload_png;
    const int            icon_upload_pngSize = 2513;

    extern const char*   icon_vocal_png;
    const int            icon_vocal_pngSize = 23916;

    extern const char*   image_png;
    const int            image_pngSize = 2301;

    extern const char*   microphone_1_png;
    const int            microphone_1_pngSize = 6932;

    extern const char*   music_png;
    const int            music_pngSize = 4618;

    extern const char*   render_mark_png;
    const int            render_mark_pngSize = 49501;

    // Number of elements in the namedResourceList and originalFileNames arrays.
    const int namedResourceListSize = 29;

    // Points to the start of a list of resource names.
    extern const char* namedResourceList[];

    // Points to the start of a list of resource filenames.
    extern const char* originalFilenames[];

    // If you provide the name of one of the binary resource variables above, this function will
    // return the corresponding data and its size (or a null pointer if the name isn't found).
    const char* getNamedResource (const char* resourceNameUTF8, int& dataSizeInBytes);

    // If you provide the name of one of the binary resource variables above, this function will
    // return the corresponding original, non-mangled filename (or a null pointer if the name isn't found).
    const char* getNamedResourceOriginalFilename (const char* resourceNameUTF8);
}
