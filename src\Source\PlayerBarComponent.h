#pragma once
#include <JuceHeader.h>
#include <juce_audio_utils/juce_audio_utils.h>

struct AudioFileInfo
{
    juce::File file;
    juce::String lastPlayed;
    double totalTimeSeconds = 0.0; // 
};

class PlayerBarComponent : public juce::Component, public juce::ChangeListener, public juce::Timer
{
public:
    PlayerBarComponent();
    void paint(juce::Graphics &g) override;
    void resized() override; //  line
    // void mouseDown(const juce::MouseEvent& event) override;
    // void mouseDrag(const juce::MouseEvent& event) override;
    void mouseUp(const juce::MouseEvent& event) override;
    void mouseMove(const juce::MouseEvent& event) override;
    void mouseExit(const juce::MouseEvent& event) override;
    void timerCallback() override;

    void prepareToPlay(int samplesPerBlockExpected, double sampleRate);
    void getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill);
    void releaseResources();
    void loadAudioFile(const juce::File& file);
    void loadAndPlayFile(const juce::File& file);
    
    ~PlayerBarComponent();
    void changeListenerCallback(juce::ChangeBroadcaster* source) override;

    juce::File getPluginStaticFolder();

private:
    juce::Image iconMenu, iconPrev, iconPlay, iconPause, iconNext, iconHeart, iconOpen;
    bool isPlaying = false;
    bool openIconHovered = false;
    bool isDraggingSlider = false;
    
    juce::Array<AudioFileInfo> playHistory;
    
    juce::AudioFormatManager formatManager;
    
    std::unique_ptr<juce::AudioFormatReaderSource> readerSource;
    std::unique_ptr<juce::FileChooser> fileChooser;
    juce::AudioDeviceManager audioDeviceManager;
    juce::AudioSourcePlayer audioSourcePlayer;
    juce::AudioTransportSource transportSource;
    juce::Slider seekSlider;
    juce::File currentAudioFile;
    bool userIsDragging = false;
    bool userStopped = false;

    // Icon layout
    const int iconAreaY = seekSlider.getBottom() + 20;
};

class FileSelectDialog : public juce::Component, public juce::ListBoxModel
{
public:
    FileSelectDialog(const juce::Array<AudioFileInfo>& history, std::function<void(const juce::File&)> onSelect);

    int getNumRows() override;
    void paintListBoxItem(int row, juce::Graphics&, int width, int height, bool rowIsSelected) override;
    void listBoxItemClicked(int row, const juce::MouseEvent&) override;
    void resized() override;

private:
    juce::ListBox listBox;
    juce::Array<AudioFileInfo> historyList;
    std::function<void(const juce::File&)> onSelectFile;
};