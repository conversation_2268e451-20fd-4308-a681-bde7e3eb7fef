#pragma once

#include <JuceHeader.h>

class MixMasterPageComponent : public juce::Component
{
public:
    MixMasterPageComponent();
    ~MixMasterPageComponent() override;
    void paint(juce::Graphics &g) override;
    void resized() override;

private:
    juce::TextButton uploadButton;
    std::unique_ptr<juce::Drawable> icon_mix_upload;

    JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(MixMasterPageComponent)
};