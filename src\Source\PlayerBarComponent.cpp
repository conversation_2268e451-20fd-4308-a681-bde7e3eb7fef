#include "PlayerBarComponent.h"
#include "BinaryData.h"

// static juce::AudioDeviceManager audioDeviceManager;
// // static std::unique_ptr<PlayerBarComponent> playerBarComponentPointer;
// static juce::AudioSourcePlayer audioSourcePlayer;

// --- Consistent layout constants ---
constexpr int kOuterMargin = 24;
constexpr int kBarWidthMax = 430;
constexpr int kBarHeight = 100;
constexpr int kInnerPadding = 28;
constexpr int kProgressBarHeight = 6;
constexpr int kKnobRadius = 10;
constexpr int kIconSize = 20;
constexpr int kPlaySize = 47;
constexpr int kNumIcons = 5;

PlayerBarComponent::PlayerBarComponent()
{
    iconMenu  = juce::ImageCache::getFromMemory(BinaryData::icon_menu_png,  BinaryData::icon_menu_pngSize);
    iconPrev  = juce::ImageCache::getFromMemory(BinaryData::icon_prev_png,  BinaryData::icon_prev_pngSize);
    iconPlay  = juce::ImageCache::getFromMemory(BinaryData::icon_play_png,  BinaryData::icon_play_pngSize);
    iconPause = juce::ImageCache::getFromMemory(BinaryData::icon_pause_png, BinaryData::icon_pause_pngSize);
    iconNext  = juce::ImageCache::getFromMemory(BinaryData::icon_next_png,  BinaryData::icon_next_pngSize);
    iconOpen  = juce::ImageCache::getFromMemory(BinaryData::icon_open_png,  BinaryData::icon_open_pngSize); 

    formatManager.registerBasicFormats();
    transportSource.addChangeListener(this);

    seekSlider.setSliderStyle(juce::Slider::LinearBar);
    seekSlider.setTextBoxStyle(juce::Slider::NoTextBox, false, 0, 0);
    seekSlider.setRange(0.0, 1.0, 0.0001);
    seekSlider.onValueChange = [this] {
        if (readerSource && transportSource.getLengthInSeconds() > 0.0)
        {
            double total = transportSource.getLengthInSeconds();
            transportSource.setPosition(seekSlider.getValue() * total);

            // Only start playback if not stopped by user
            if (!transportSource.isPlaying() && !userStopped)
            {
                isPlaying = true;
                transportSource.start();
                startTimerHz(30);
                repaint();
            }
        }
    };
    seekSlider.onDragStart = [this] { isDraggingSlider = true; };
    seekSlider.onDragEnd = [this] { isDraggingSlider = false; };
    addAndMakeVisible(seekSlider);
    
    seekSlider.setSliderSnapsToMousePosition(true);
    seekSlider.setValue(0.0, juce::dontSendNotification);

    // Audio device setup (reference style)
    audioDeviceManager.initialiseWithDefaultDevices(2, 2);
    audioSourcePlayer.setSource(&transportSource);
    audioDeviceManager.addAudioCallback(&audioSourcePlayer);

    seekSlider.setColour(juce::Slider::trackColourId, juce::Colours::white);
    // seekSlider.setColour(juce::Slider::backgroundColourId, juce::Colours::lightgrey);
    // seekSlider.setColour(juce::Slider::thumbColourId, juce::Colours::orange);
}

PlayerBarComponent::~PlayerBarComponent()
{
    // Remove audio callback and disconnect sources before destroying members
    audioSourcePlayer.setSource(nullptr);
    audioDeviceManager.removeAudioCallback(&audioSourcePlayer);

    transportSource.setSource(nullptr);
    transportSource.removeChangeListener(this);
}

void PlayerBarComponent::paint(juce::Graphics &g)
{
    const int barWidth = juce::jmin(getWidth() - 2 * kOuterMargin, kBarWidthMax);
    const int barX = (getWidth() - barWidth) / 2;
    const int barY = getHeight() - kBarHeight - kOuterMargin;

    juce::ColourGradient gradient(
        juce::Colour::fromRGB(90, 120, 255), 0, (float)barY,
        juce::Colour::fromRGB(120, 60, 220), 0, (float)(barY + kBarHeight), false);
    g.setGradientFill(gradient);
    g.fillRoundedRectangle((float)barX, (float)barY, (float)barWidth, (float)kBarHeight, 10.0f);

    // Get slider bounds
    auto sliderBounds = seekSlider.getBounds();

    // Calculate current time and total length
    double currentTime = transportSource.getCurrentPosition();
    double totalTime = transportSource.getLengthInSeconds();

    // Format as mm:ss
    auto formatTime = [](double seconds) {
        int mins = static_cast<int>(seconds) / 60;
        int secs = static_cast<int>(seconds) % 60;
        return juce::String(mins).paddedLeft('0', 2) + ":" + juce::String(secs).paddedLeft('0', 2);
    };

    juce::String currentTimeStr = formatTime(currentTime);
    juce::String totalTimeStr = formatTime(totalTime);

    g.setColour(juce::Colours::white);
    g.setFont(14.0f);

    // Draw current time at the left of the slider
    g.drawText(currentTimeStr,
           sliderBounds.getX() - 44, // align with reserved space
           sliderBounds.getY() + (sliderBounds.getHeight() - 18) / 2,
           40, 18, juce::Justification::right);

    // Draw total time at the right of the slider
    g.drawText(totalTimeStr,
            sliderBounds.getRight() + 4, // align with reserved space
            sliderBounds.getY() + (sliderBounds.getHeight() - 18) / 2,
            40, 18, juce::Justification::left);

    // Icon layout
    int iconAreaY = seekSlider.getBottom() + 20;
    const int iconAreaHeight = barY + kBarHeight - iconAreaY - 10;
    int totalWidth = 2 * kIconSize + 2 * kIconSize + kPlaySize;
    int iconSpacing = (barWidth - totalWidth) / (kNumIcons + 1);

    int prevIconX  = barX + iconSpacing;
    int openIconX  = prevIconX + kIconSize + iconSpacing;
    int playIconX  = openIconX + kIconSize + iconSpacing;
    int menuIconX  = playIconX + kPlaySize + iconSpacing;
    int nextIconX  = menuIconX + kIconSize + iconSpacing;
    int iconY = iconAreaY + (iconAreaHeight - kIconSize) / 2;
    int playIconY = iconAreaY + (iconAreaHeight - kPlaySize) / 2;


    juce::Rectangle<int> prevBounds(prevIconX, iconY, kIconSize, kIconSize);
    juce::Rectangle<int> nextBounds(nextIconX, iconY, kIconSize, kIconSize);

    if (!iconPrev.isNull())
        g.drawImageWithin(iconPrev, prevIconX, iconY, kIconSize, kIconSize, juce::RectanglePlacement::centred);

    if (!iconOpen.isNull())
        g.drawImageWithin(iconOpen, openIconX, iconY, kIconSize, kIconSize, juce::RectanglePlacement::centred);

    if (!isPlaying && !iconPlay.isNull())
        g.drawImageWithin(iconPlay, playIconX, playIconY, kPlaySize, kPlaySize, juce::RectanglePlacement::centred);
    else if (isPlaying && !iconPause.isNull())
        g.drawImageWithin(iconPause, playIconX, playIconY, kPlaySize, kPlaySize, juce::RectanglePlacement::centred);

    if (!iconMenu.isNull())
        g.drawImageWithin(iconMenu, menuIconX, iconY, kIconSize, kIconSize, juce::RectanglePlacement::centred);

    if (!iconNext.isNull())
        g.drawImageWithin(iconNext, nextIconX, iconY, kIconSize, kIconSize, juce::RectanglePlacement::centred);
}



void PlayerBarComponent::prepareToPlay(int samplesPerBlockExpected, double sampleRate)
{
    transportSource.prepareToPlay(samplesPerBlockExpected, sampleRate);
}

void PlayerBarComponent::getNextAudioBlock(const juce::AudioSourceChannelInfo& bufferToFill)
{
    transportSource.getNextAudioBlock(bufferToFill);
}

void PlayerBarComponent::releaseResources()
{
    transportSource.releaseResources();
}

// void PlayerBarComponent::mouseDown(const juce::MouseEvent& event)
// {
//     const int barWidth = juce::jmin(getWidth() - 2 * kOuterMargin, kBarWidthMax);
//     const int barX = (getWidth() - barWidth) / 2;
//     const int barY = getHeight() - kBarHeight - kOuterMargin;
//     const int progressBarY = barY + 18;
//     const int progressBarWidth = barWidth - 2 * kInnerPadding;

//     juce::Rectangle<int> progressBarBounds(barX + kInnerPadding, progressBarY, progressBarWidth, kProgressBarHeight + 10);

//     if (progressBarBounds.contains(event.getPosition()))
//     {
//         isDraggingSlider = true;
//         mouseDrag(event);
//     }
// }

// void PlayerBarComponent::mouseDrag(const juce::MouseEvent& event)
// {
//     if (!isDraggingSlider)
//         return;

//     const int barWidth = juce::jmin(getWidth() - 2 * kOuterMargin, kBarWidthMax);
//     const int barX = (getWidth() - barWidth) / 2;
//     const int barY = getHeight() - kBarHeight - kOuterMargin;
//     const int progressBarY = barY + 18;
//     const int progressBarWidth = barWidth - 2 * kInnerPadding;

//     int mouseX = event.getPosition().getX();
//     float rel = juce::jlimit(0.0f, 1.0f, (mouseX - (barX + kInnerPadding)) / (float)progressBarWidth);

//     if (readerSource && transportSource.getLengthInSeconds() > 0.0)
//     {
//         double totalLength = transportSource.getLengthInSeconds();
//         transportSource.setPosition(rel * totalLength);
//         repaint();
//     }
// }

juce::File PlayerBarComponent::getPluginStaticFolder()
{
    auto documents = juce::File::getSpecialLocation(juce::File::userDocumentsDirectory);
    juce::File staticFolder = documents.getChildFile("AIpluginStatic");
    if (!staticFolder.exists())
        staticFolder.createDirectory();
    return staticFolder;
}


void PlayerBarComponent::mouseUp(const juce::MouseEvent& event)
{
    isDraggingSlider = false;

    const int barWidth = juce::jmin(getWidth() - 2 * kOuterMargin, kBarWidthMax);
    const int barX = (getWidth() - barWidth) / 2;
    const int barY = getHeight() - kBarHeight - kOuterMargin;
    const int progressBarY = barY + 18;
    const int progressBarWidth = barWidth - 2 * kInnerPadding;
    const int iconAreaY = progressBarY + kProgressBarHeight + 20;
    const int iconAreaHeight = barY + kBarHeight - iconAreaY - 10;
    int totalWidth = 2 * kIconSize + 2 * kIconSize + kPlaySize;
    int iconSpacing = (barWidth - totalWidth) / (kNumIcons + 1);

    int prevIconX  = barX + iconSpacing;
    int openIconX  = prevIconX + kIconSize + iconSpacing;
    int playIconX  = openIconX + kIconSize + iconSpacing;
    int menuIconX  = playIconX + kPlaySize + iconSpacing;
    int nextIconX  = menuIconX + kIconSize + iconSpacing;
    int iconY = iconAreaY + (iconAreaHeight - kIconSize) / 2;

    int playIconY = iconAreaY + (iconAreaHeight - kPlaySize) / 2;

    juce::Rectangle<int> playBounds(playIconX, playIconY, kPlaySize, kPlaySize);
    juce::Rectangle<int> prevBounds(prevIconX, iconY, kIconSize, kIconSize);
    juce::Rectangle<int> nextBounds(nextIconX, iconY, kIconSize, kIconSize);

    // Play button logic
    if (playBounds.contains(event.getPosition()))
    {
        if (!isPlaying && readerSource && transportSource.getLengthInSeconds() > 0.0)
        {
            // If at end, reset to start
            if (transportSource.hasStreamFinished() ||
                transportSource.getCurrentPosition() >= transportSource.getLengthInSeconds())
            {
                transportSource.setPosition(0.0);
            }
            isPlaying = true;
            userStopped = false; // <--- reset flag
            transportSource.start();
            startTimerHz(30);
            repaint();
            return;
        }
        else if (isPlaying)
        {
            transportSource.stop();
            isPlaying = false;
            userStopped = true; // <--- set flag
            stopTimer();
            repaint();
            return;
        }
    }

    // juce::Rectangle<int> openBounds(openIconX, iconAreaY + (iconAreaHeight - kIconSize) / 2, kIconSize, kIconSize);
    juce::Rectangle<int> openBounds(openIconX, iconAreaY + (iconAreaHeight - kIconSize) / 2, kIconSize, kIconSize);

    if (openBounds.contains(event.getPosition()))
    {
        fileChooser = std::make_unique<juce::FileChooser>(
            "Select an audio file...", juce::File{}, "*.wav;*.mp3;*.flac");

        auto* chooserPtr = fileChooser.get();

        fileChooser->launchAsync(
            juce::FileBrowserComponent::openMode | juce::FileBrowserComponent::canSelectFiles,
            [this, chooserPtr](const juce::FileChooser& fc)
            {
                juce::File selectedFile = fc.getResult();
                if (selectedFile.existsAsFile())
                {
                    loadAndPlayFile(selectedFile); // <-- Always use this!
                }
                if (fileChooser.get() == chooserPtr)
                    fileChooser.reset();
            });
    }

    // int menuIconX  = playIconX + kPlaySize + iconSpacing;
    // int iconY = iconAreaY + (iconAreaHeight - kIconSize) / 2;
    juce::Rectangle<int> menuBounds(menuIconX, iconY, kIconSize, kIconSize);

    // List icon logic
    if (menuBounds.contains(event.getPosition()))
    {
        if (playHistory.isEmpty())
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::InfoIcon,
                "History",
                "No played audio history.");
            return;
        }

        auto* dialog = new juce::DialogWindow("Recent Audio History", juce::Colours::lightgrey, true);
        dialog->setContentOwned(new FileSelectDialog(playHistory, [this](const juce::File& file) {
            loadAndPlayFile(file);
        }), true);
        dialog->setUsingNativeTitleBar(true); // enable native close button
        dialog->centreWithSize(400, 240);
        dialog->enterModalState(true, nullptr, true);
        return;
    }

    // Prev icon logic
    if (prevBounds.contains(event.getPosition()))
    {
        if (currentAudioFile.existsAsFile())
        {
            juce::File folder = currentAudioFile.getParentDirectory();
            juce::Array<juce::File> audioFiles = folder.findChildFiles(juce::File::findFiles, false, "*.wav;*.mp3;*.flac");
            audioFiles.sort(); // Alphabetical order

            // Use full path for comparison
            int idx = -1;
            for (int i = 0; i < audioFiles.size(); ++i)
            {
                if (audioFiles[i].getFullPathName() == currentAudioFile.getFullPathName())
                {
                    idx = i;
                    break;
                }
            }
            if (idx > 0)
            {
                loadAndPlayFile(audioFiles[idx - 1]);
            }
        }
        return;
    }

    // Next icon logic
    if (nextBounds.contains(event.getPosition()))
    {
        if (currentAudioFile.existsAsFile())
        {
            juce::File folder = currentAudioFile.getParentDirectory();
            juce::Array<juce::File> audioFiles = folder.findChildFiles(juce::File::findFiles, false, "*.wav;*.mp3;*.flac");
            audioFiles.sort(); // Alphabetical order

            // Use full path for comparison
            int idx = -1;
            for (int i = 0; i < audioFiles.size(); ++i)
            {
                if (audioFiles[i].getFullPathName() == currentAudioFile.getFullPathName())
                {
                    idx = i;
                    break;
                }
            }
            if (idx >= 0 && idx < audioFiles.size() - 1)
            {
                loadAndPlayFile(audioFiles[idx + 1]);
            }
        }
        return;
    }
}

void PlayerBarComponent::timerCallback()
{
    repaint();
    if (readerSource && transportSource.hasStreamFinished())
    {
        transportSource.stop();
        stopTimer();
        isPlaying = false;
        userStopped = false; // <--- reset flag
        repaint();
    }

    if (readerSource && transportSource.getLengthInSeconds() > 0.0 && !isDraggingSlider)
    {
        double pos = transportSource.getCurrentPosition();
        double len = transportSource.getLengthInSeconds();
        seekSlider.setValue(pos / len, juce::dontSendNotification);
    }
}

void PlayerBarComponent::changeListenerCallback(juce::ChangeBroadcaster* source)
{
    if (source == &transportSource)
    {
        if (transportSource.isPlaying())
        {
            isPlaying = true;
            userStopped = false; // <--- reset flag
            startTimerHz(30);
        }
        else
        {
            isPlaying = false;
            stopTimer();
        }
        repaint();
    }
}

// void setupAudioDeviceManager()
// {
//     audioDeviceManager.initialiseWithDefaultDevices(2, 2);
//     // playerBarComponentPointer = std::make_unique<PlayerBarComponent>();
//     audioSourcePlayer.setSource(&playerBarComponentPointer->transportSource);
//     audioDeviceManager.addAudioCallback(&audioSourcePlayer);
// }

void PlayerBarComponent::mouseMove(const juce::MouseEvent&) {}
void PlayerBarComponent::mouseExit(const juce::MouseEvent&) {}

void PlayerBarComponent::resized()
{
    const int timeLabelWidth = 25; // Reserve space for time labels
    const int barWidth = juce::jmin(getWidth() - 2 * kOuterMargin, kBarWidthMax);
    const int barX = (getWidth() - barWidth) / 2;
    const int barY = getHeight() - kBarHeight - kOuterMargin;
    const int progressBarY = barY + 18;
    const int progressBarWidth = barWidth - 2 * kInnerPadding - 2 * timeLabelWidth; // Subtract label space
    seekSlider.setBounds(barX + kInnerPadding + timeLabelWidth, progressBarY, progressBarWidth, kProgressBarHeight + 10);
}


void PlayerBarComponent::loadAndPlayFile(const juce::File& file)
{
    transportSource.stop();
    transportSource.setSource(nullptr);
    readerSource.reset();

    auto* reader = formatManager.createReaderFor(file);
    if (reader != nullptr)
    {
        std::unique_ptr<juce::AudioFormatReaderSource> newSource(new juce::AudioFormatReaderSource(reader, true));
        readerSource = std::move(newSource);
        transportSource.setSource(readerSource.get(), 0, nullptr, reader->sampleRate);
        transportSource.setPosition(0.0);
        isPlaying = true;
        currentAudioFile = file;

        double totalSecs = reader->lengthInSamples / reader->sampleRate; // <-- Get total time

        // --- Add/update history ---
        auto now = juce::Time::getCurrentTime().toString(true, true);
        bool found = false;
        for (auto& info : playHistory)
        {
            if (info.file == file)
            {
                info.lastPlayed = now;
                info.totalTimeSeconds = totalSecs; // <-- Update time
                found = true;
                break;
            }
        }
        if (!found)
            playHistory.add({ file, now, totalSecs }); // <-- Store time

        transportSource.start();
        startTimerHz(30);
        repaint();
    }
}



void FileSelectDialog::resized()
{
    listBox.setBounds(getLocalBounds());
}

FileSelectDialog::FileSelectDialog(const juce::Array<AudioFileInfo>& history, std::function<void(const juce::File&)> onSelect)
    : historyList(history), onSelectFile(std::move(onSelect))
{
    listBox.setModel(this);
    addAndMakeVisible(listBox);
    listBox.setRowHeight(36); // or 40 for even more space
    setSize(400, 220);
}

int FileSelectDialog::getNumRows()
{
    return historyList.size();
}

void FileSelectDialog::paintListBoxItem(int row, juce::Graphics& g, int width, int height, bool rowIsSelected)
{
    if (rowIsSelected)
        g.fillAll(juce::Colour(0xffdbeafe)); // soft blue highlight

    if (row >= 0 && row < historyList.size())
    {
        auto& info = historyList[row];
        int mins = static_cast<int>(info.totalTimeSeconds) / 60;
        int secs = static_cast<int>(info.totalTimeSeconds) % 60;
        juce::String timeStr = juce::String(mins).paddedLeft('0', 2) + ":" + juce::String(secs).paddedLeft('0', 2);

        // Filename (bold)
        g.setColour(juce::Colours::black);
        g.setFont(juce::Font(16.0f, juce::Font::bold));
        g.drawText(info.file.getFileName(), 12, 4, width - 24, 18, juce::Justification::left);

        // Time and last played (smaller, lighter)
        g.setColour(juce::Colours::darkgrey);
        g.setFont(juce::Font(13.0f));
        g.drawText("[" + timeStr + "]   Last played: " + info.lastPlayed,
                   12, 22, width - 24, 14, juce::Justification::left);

        // Separator line
        g.setColour(juce::Colour(0xffe5e7eb));
        g.drawLine(0, height - 1, (float)width, height - 1, 1.0f);
    }
}

void FileSelectDialog::listBoxItemClicked(int row, const juce::MouseEvent&)
{
    if (row >= 0 && row < historyList.size())
    {
        onSelectFile(historyList[row].file);
        if (auto* dw = findParentComponentOfClass<juce::DialogWindow>())
            dw->exitModalState(1);
    }
}

