#pragma once

#include <JuceHeader.h>
#include <juce_core/juce_core.h>
#include <juce_data_structures/juce_data_structures.h>
#include "AudioSegmentMerger.h"
#include <fstream>
#include "PlayerBarComponent.h"

class UploadPanel : public juce::Component
{
public:
    void paint(juce::Graphics &g) override;
};

class NoiseRemoverComponent : public juce::Component,
                              public juce::Button::Listener
{
public:
    NoiseRemoverComponent();
    void resized() override;
    static juce::File getLogFile();
    void buttonClicked(juce::Button *button) override;
    void startNoiseRemovalProcess(const juce::File &file);
    void requestNoiseRemovalEdit(const juce::String &fileId);
    void pollForResult(const juce::String& jobId, const juce::String& fileId);
    void downloadProcessedFile(const juce::String& downloadUrl, const juce::String& outputPath);
    juce::File getPluginStaticFolder();
    PlayerBarComponent* playerBarComponent = nullptr;


private:
    UploadPanel uploadPanel;
    juce::ImageComponent uploadIcon;
    juce::Label instructionLabel;
    juce::Label formatLabel;
    juce::TextButton uploadButton;
    std::unique_ptr<juce::FileChooser> chooser;
    // juce::String apiKey;
    std::atomic<bool> busy{false};
    std::unique_ptr<juce::AlertWindow> progressWindow;
    juce::File selectedFile;


    //JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(NoiseRemoverComponent)
};