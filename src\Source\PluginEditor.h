/*
  ==============================================================================

    This file contains the basic framework code for a JUCE plugin editor.

  ==============================================================================
*/

#pragma once

#include <JuceHeader.h>
#include "PluginProcessor.h"
#include "SidebarComponent.h"
#include "MainPanelComponent.h"
#include "PlayerBarComponent.h"

//==============================================================================
/**
 */
class AIpluginAudioProcessorEditor : public juce::AudioProcessorEditor
{
public:
  AIpluginAudioProcessorEditor(AIpluginAudioProcessor &);
  ~AIpluginAudioProcessorEditor() override;

  //==============================================================================
  void paint(juce::Graphics &) override;
  void resized() override;

private:
  // This reference is provided as a quick way for your editor to
  // access the processor object that created it.
  AIpluginAudioProcessor &audioProcessor;
  MainPanelComponent mainPanel;
  // PlayerBarComponent playerBar;
  juce::Slider gainSlider;
  JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(AIpluginAudioProcessorEditor)
};
