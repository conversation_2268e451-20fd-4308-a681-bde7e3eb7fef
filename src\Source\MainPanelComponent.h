#pragma once
#include <JuceHeader.h>
#include "PlayerBarComponent.h"
#include "CreatePageComponent.h"
#include "SidebarComponent.h"
#include "KeyBpmFinderComponent.h"
#include "TraineClonePageComponent.h"
#include "NoiseRemoverPageComponent.h"
#include "SplitStemPageComponent.h"
#include "MixMasterPageComponent.h"
#include "LibraryPageComponent.h"

class MainPanelComponent : public juce::Component
{
private:
    SidebarComponent sidebarComponent;
    PlayerBarComponent playerBar;
    juce::TextEditor searchBox;
    juce::Image logoImage;
    std::unique_ptr<CreatePageComponent> createPage;
    std::unique_ptr<TrainClonePageComponent> trainClonePage;
    std::unique_ptr<KeyBpmFinderComponent> keyBpmFinderPage;
    std::unique_ptr<NoiseRemoverComponent> noiseRemoverPage;
    std::unique_ptr<SplitSteamPageComponent> splitSteamPage;
    std::unique_ptr<MixMasterPageComponent> mixMasterPage;
    std::unique_ptr<LibraryPageComponent> libraryPage;

    void showPage(int pageIndex);

public:
    MainPanelComponent();
    void paint(juce::Graphics &g) override;
    void resized() override;
};