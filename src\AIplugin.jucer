<?xml version="1.0" encoding="UTF-8"?>

<JUCERPROJECT id="jT5N5u" name="AIplugin" projectType="audioplug" useAppConfig="0"
              addUsingNamespaceToJuceHeader="0" jucerFormatVersion="1" pluginCharacteristicsValue="pluginEditorRequiresKeys,pluginProducesMidiOut,pluginWantsMidiIn"
              pluginFormats="buildAU,buildVST3" maxBinaryFileSize="20971520">
  <MAINGROUP id="koTmlX" name="AIplugin">
    <GROUP id="{2A49C9E7-ACE8-7296-3D16-5DA9FFDDCB65}" name="Source">
      <FILE id="ASl2T6" name="SplitStemPageComponent.cpp" compile="1" resource="0"
            file="Source/SplitStemPageComponent.cpp"/>
      <FILE id="Z8yH5v" name="SplitStemPageComponent.h" compile="0" resource="0"
            file="Source/SplitStemPageComponent.h"/>
      <FILE id="qHQR4p" name="Constant.h" compile="0" resource="0" file="Source/Constant.h"/>
      <FILE id="N04ujH" name="AudioSegmentMerger.cpp" compile="1" resource="0"
            file="Source/AudioSegmentMerger.cpp"/>
      <FILE id="EjEBMd" name="CreatePageComponent.cpp" compile="1" resource="0"
            file="Source/CreatePageComponent.cpp"/>
      <FILE id="yRQwkY" name="KeyBpmFinderComponent.cpp" compile="1" resource="0"
            file="Source/KeyBpmFinderComponent.cpp"/>
      <FILE id="HM1v1v" name="LibraryPageComponent.cpp" compile="1" resource="0"
            file="Source/LibraryPageComponent.cpp"/>
      <FILE id="QAOtnm" name="MainPanelComponent.cpp" compile="1" resource="0"
            file="Source/MainPanelComponent.cpp"/>
      <FILE id="eUKmBA" name="MixMasterPageComponent.cpp" compile="1" resource="0"
            file="Source/MixMasterPageComponent.cpp"/>
      <FILE id="LroCc1" name="PlayerBarComponent.cpp" compile="1" resource="0"
            file="Source/PlayerBarComponent.cpp"/>
      <FILE id="Hs5yPu" name="SidebarComponent.cpp" compile="1" resource="0"
            file="Source/SidebarComponent.cpp"/>
      <FILE id="eKsJRh" name="TraineClonePageComponent.cpp" compile="1" resource="0"
            file="Source/TraineClonePageComponent.cpp"/>
      <FILE id="zQHeFY" name="NoiseRemoverPageComponent.cpp" compile="1"
            resource="0" file="Source/NoiseRemoverPageComponent.cpp"/>
      <FILE id="CWYlkl" name="AudioSegmentMerger.h" compile="0" resource="0"
            file="Source/AudioSegmentMerger.h"/>
      <GROUP id="{E309E88A-633F-B558-AACB-C139692D02F0}" name="UserDownloads"/>
      <GROUP id="{23AFC705-5DA6-B5E6-4D7B-D80CBA4BD25F}" name="Resources">
        <FILE id="gS1f2g" name="icon_bass.png" compile="0" resource="1" file="Source/Resources/icon_bass.png"/>
        <FILE id="oo561W" name="icon_create.png" compile="0" resource="1" file="Source/Resources/icon_create.png"/>
        <FILE id="KzbjVZ" name="icon_drum.png" compile="0" resource="1" file="Source/Resources/icon_drum.png"/>
        <FILE id="YW0el7" name="icon_favourite.png" compile="0" resource="1"
              file="Source/Resources/icon_favourite.png"/>
        <FILE id="UoAFx4" name="icon_guitar.png" compile="0" resource="1" file="Source/Resources/icon_guitar.png"/>
        <FILE id="ea7NOV" name="icon_keybpm.png" compile="0" resource="1" file="Source/Resources/icon_keybpm.png"/>
        <FILE id="LC6bBY" name="icon_library.png" compile="0" resource="1"
              file="Source/Resources/icon_library.png"/>
        <FILE id="k28HPL" name="icon_menu.png" compile="0" resource="1" file="Source/Resources/icon_menu.png"/>
        <FILE id="yO3y5y" name="icon_micro.png" compile="0" resource="1" file="Source/Resources/icon_micro.png"/>
        <FILE id="JrcwbT" name="icon_mini_music.png" compile="0" resource="1"
              file="Source/Resources/icon_mini_music.png"/>
        <FILE id="DLwrR0" name="icon_mix.png" compile="0" resource="1" file="Source/Resources/icon_mix.png"/>
        <FILE id="Vivh5E" name="icon_mix_upload.png" compile="0" resource="1"
              file="Source/Resources/icon_mix_upload.png"/>
        <FILE id="Dv8IX6" name="icon_music.png" compile="0" resource="1" file="Source/Resources/icon_music.png"/>
        <FILE id="D95AtZ" name="icon_next.png" compile="0" resource="1" file="Source/Resources/icon_next.png"/>
        <FILE id="epHQ61" name="icon_noise.png" compile="0" resource="1" file="Source/Resources/icon_noise.png"/>
        <FILE id="FlBGZv" name="icon_open.png" compile="0" resource="1" file="Source/Resources/icon_open.png"/>
        <FILE id="WNGuqD" name="icon_other.png" compile="0" resource="1" file="Source/Resources/icon_other.png"/>
        <FILE id="b94MI5" name="icon_pause.png" compile="0" resource="1" file="Source/Resources/icon_pause.png"/>
        <FILE id="bgyQfP" name="icon_piano.png" compile="0" resource="1" file="Source/Resources/icon_piano.png"/>
        <FILE id="wgkUJQ" name="icon_play.png" compile="0" resource="1" file="Source/Resources/icon_play.png"/>
        <FILE id="u1hFK6" name="icon_prev.png" compile="0" resource="1" file="Source/Resources/icon_prev.png"/>
        <FILE id="AFFap9" name="icon_split.png" compile="0" resource="1" file="Source/Resources/icon_split.png"/>
        <FILE id="QIat6s" name="icon_train.png" compile="0" resource="1" file="Source/Resources/icon_train.png"/>
        <FILE id="IHoHjQ" name="icon_upload.png" compile="0" resource="1" file="Source/Resources/icon_upload.png"/>
        <FILE id="mt59S4" name="icon_vocal.png" compile="0" resource="1" file="Source/Resources/icon_vocal.png"/>
        <FILE id="MAbruj" name="image.png" compile="0" resource="1" file="Source/Resources/image.png"/>
        <FILE id="MCeqng" name="microphone (1).png" compile="0" resource="1"
              file="Source/Resources/microphone (1).png"/>
        <FILE id="UMiaIr" name="music.png" compile="0" resource="1" file="Source/Resources/music.png"/>
        <FILE id="rlde8e" name="render_mark.png" compile="0" resource="1" file="Source/Resources/render_mark.png"/>
      </GROUP>
      <FILE id="OoVdAp" name="LibraryPageComponent.h" compile="0" resource="0"
            file="Source/LibraryPageComponent.h"/>
      <FILE id="moC59F" name="MixMasterPageComponent.h" compile="0" resource="0"
            file="Source/MixMasterPageComponent.h"/>
      <FILE id="OvcLIg" name="NoiseRemoverPageComponent.h" compile="0" resource="0"
            file="Source/NoiseRemoverPageComponent.h"/>
      <FILE id="b6m1V3" name="KeyBpmFinderComponent.h" compile="0" resource="0"
            file="Source/KeyBpmFinderComponent.h"/>
      <FILE id="MqrdO6" name="TraineClonePageComponent.h" compile="0" resource="0"
            file="Source/TraineClonePageComponent.h"/>
      <FILE id="Cvqbrs" name="CreatePageComponent.h" compile="0" resource="0"
            file="Source/CreatePageComponent.h"/>
      <FILE id="i7VpF6" name="MainPanelComponent.h" compile="0" resource="0"
            file="Source/MainPanelComponent.h"/>
      <FILE id="BrIS67" name="PlayerBarComponent.h" compile="0" resource="0"
            file="Source/PlayerBarComponent.h"/>
      <FILE id="RwjmUV" name="SidebarComponent.h" compile="0" resource="0"
            file="Source/SidebarComponent.h"/>
      <FILE id="JsMMao" name="PluginProcessor.cpp" compile="1" resource="0"
            file="Source/PluginProcessor.cpp"/>
      <FILE id="hmdSxV" name="PluginProcessor.h" compile="0" resource="0"
            file="Source/PluginProcessor.h"/>
      <FILE id="GGi1t0" name="PluginEditor.cpp" compile="1" resource="0"
            file="Source/PluginEditor.cpp"/>
      <FILE id="MH6xDv" name="PluginEditor.h" compile="0" resource="0" file="Source/PluginEditor.h"/>
    </GROUP>
  </MAINGROUP>
  <MODULES>
    <MODULE id="juce_audio_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_devices" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_formats" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_plugin_client" showAllCode="1" useLocalCopy="0"
            useGlobalPath="1"/>
    <MODULE id="juce_audio_processors" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_audio_utils" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_core" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_data_structures" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_events" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_graphics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_gui_basics" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
    <MODULE id="juce_gui_extra" showAllCode="1" useLocalCopy="0" useGlobalPath="1"/>
  </MODULES>
  <JUCEOPTIONS JUCE_STRICT_REFCOUNTEDPOINTER="1" JUCE_VST3_CAN_REPLACE_VST2="0"/>
  <EXPORTFORMATS>
    <VS2022 targetFolder="Builds/VisualStudio2022" externalLibraries="aubio">
      <CONFIGURATIONS>
        <CONFIGURATION isDebug="1" name="Debug" targetName="AIplugin" headerPath="D:\project\VST plugin\Render-Fusion-MVP-2\src\library\aubio-0.4.6-win64\include"
                       libraryPath="D:\project\VST plugin\Render-Fusion-MVP-2\src\library\aubio-0.4.6-win64\lib"/>
        <CONFIGURATION isDebug="0" name="Release" targetName="AIplugin" libraryPath="D:\project\VST plugin\Render-Fusion-MVP-2\src\library\aubio-0.4.6-win64\lib"
                       headerPath="D:\project\VST plugin\Render-Fusion-MVP-2\src\library\aubio-0.4.6-win64\include"/>
      </CONFIGURATIONS>
      <MODULEPATHS>
        <MODULEPATH id="juce_audio_basics" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_audio_devices" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_audio_formats" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_audio_plugin_client" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_audio_processors" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_audio_utils" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_core" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_data_structures" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_events" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_graphics" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_gui_basics" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
        <MODULEPATH id="juce_gui_extra" path="C:/Users/<USER>/Downloads/juce-8.0.7-windows/JUCE/modules"/>
      </MODULEPATHS>
    </VS2022>
  </EXPORTFORMATS>
</JUCERPROJECT>
