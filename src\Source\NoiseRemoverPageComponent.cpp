#include "NoiseRemoverPageComponent.h"
#include "BinaryData.h"
#include "Constant.h"

// UploadPanel implementation
void UploadPanel::paint(juce::Graphics &g)
{
    juce::ColourGradient gradient(
        juce::Colour(0xFFC0C0C1), 0, 0,
        juce::Colour(0xFFB0B0B2), 0, (float)getHeight(),
        false);
    g.setGradientFill(gradient);
    g.fillAll();
}

// NoiseRemoverComponent implementation

NoiseRemoverComponent::NoiseRemoverComponent()
{
    addAndMakeVisible(uploadPanel);
    uploadPanel.setInterceptsMouseClicks(true, true);

    auto iconImage = juce::ImageCache::getFromMemory(BinaryData::icon_upload_png, BinaryData::icon_upload_pngSize);
    uploadIcon.setImage(iconImage, juce::RectanglePlacement::centred);
    uploadPanel.addAndMakeVisible(uploadIcon);

    instructionLabel.setText("Choose a file or drag\nit here", juce::dontSendNotification);
    instructionLabel.setFont(juce::Font(18.0f, juce::Font::bold));
    instructionLabel.setJustificationType(juce::Justification::centred);
    instructionLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF1E282F));
    uploadPanel.addAndMakeVisible(instructionLabel);

    formatLabel.setText("Supported formats:\n.mp3, .wav, FLAC", juce::dontSendNotification);
    formatLabel.setFont(juce::Font(16.0f));
    formatLabel.setJustificationType(juce::Justification::centred);
    formatLabel.setColour(juce::Label::textColourId, juce::Colour(0xFF1E282F));
    uploadPanel.addAndMakeVisible(formatLabel);

    uploadButton.setButtonText("Upload Audio");
    uploadButton.setColour(juce::TextButton::buttonColourId, juce::Colour(0xFF2C353B));
    uploadButton.setColour(juce::TextButton::buttonOnColourId, juce::Colours::darkgrey);
    uploadButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    uploadButton.setColour(juce::TextButton::textColourOnId, juce::Colours::white);
    uploadButton.addListener(this);
    uploadPanel.addAndMakeVisible(uploadButton);

}

juce::File NoiseRemoverComponent::getPluginStaticFolder()
{
    auto documents = juce::File::getSpecialLocation(juce::File::userDocumentsDirectory);
    juce::File staticFolder = documents.getChildFile("AIpluginStatic");
    if (!staticFolder.exists())
        staticFolder.createDirectory();
    return staticFolder;
}

void NoiseRemoverComponent::resized()
{
    auto area = getLocalBounds().reduced(40);
    auto panelHeight = 320;
    auto panelWidth = 280;
    auto panelX = (getWidth() - panelWidth) / 2;
    uploadPanel.setBounds(panelX, area.getY(), panelWidth, panelHeight);

    auto panelArea = uploadPanel.getLocalBounds().reduced(20);
    uploadIcon.setBounds(panelArea.removeFromTop(60));
    instructionLabel.setBounds(panelArea.removeFromTop(50));
    formatLabel.setBounds(panelArea.removeFromTop(40));

    int buttonWidth = 200;
    int buttonHeight = 40;
    int buttonMarginBottom = 55;
    int buttonY = uploadPanel.getHeight() - buttonHeight - buttonMarginBottom;
    uploadButton.setBounds((uploadPanel.getWidth() - buttonWidth) / 2, buttonY, buttonWidth, buttonHeight);
}

juce::File NoiseRemoverComponent::getLogFile()
{
    return juce::File::getSpecialLocation(juce::File::userDocumentsDirectory)
        .getChildFile("LALAL.AI")
        .getChildFile("debug.log");
}

void NoiseRemoverComponent::buttonClicked(juce::Button *button)
{
    if (button == &uploadButton && !busy)
    {
        chooser = std::make_unique<juce::FileChooser>(
            "Select an audio file...",
            juce::File{},
            "*.wav;*.mp3;*.flac",
            false);

        chooser->launchAsync(juce::FileBrowserComponent::openMode | juce::FileBrowserComponent::canSelectFiles,
                             [this](const juce::FileChooser &fc)
                             {
                                 auto file = fc.getResult();
                                 if (file.existsAsFile())
                                 {
                                    selectedFile  = file;
                                     startNoiseRemovalProcess(file);
                                 }
                             });
    }
}

void NoiseRemoverComponent::startNoiseRemovalProcess(const juce::File &file)
{
    busy = true;

    juce::MessageManager::callAsync([this]() {
        progressWindow = std::make_unique<juce::AlertWindow>(
            "Uploading...",
            "Uploading audio file, please wait...",
            juce::AlertWindow::InfoIcon);
        progressWindow->enterModalState();
    });

    std::thread([file, this]()
    {
        try
        {
            // Step 1: Upload file to LALAL.AI
            juce::MemoryBlock fileData;
            {
                juce::FileInputStream fis(file);
                if (!fis.openedOk())
                    throw std::runtime_error("Failed to open file for upload");
                fis.readIntoMemoryBlock(fileData);
            }

            juce::String uploadUrl = "https://www.lalal.ai/api/upload/";
            juce::String contentDisposition = "Content-Disposition: attachment; filename=" + file.getFileName() + "\n";
            juce::String authHeader = "Authorization: license " + juce::String(apiKey) + "\n";

            auto uploadOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inAddress)
                .withExtraHeaders(contentDisposition + authHeader)
                .withHttpRequestCmd("POST");

            auto uploadStream = juce::URL(uploadUrl)
                .withPOSTData(fileData)
                .createInputStream(uploadOptions);

            if (!uploadStream)
                throw std::runtime_error("Failed to connect to LALAL.AI API");

            juce::String uploadResponse = uploadStream->readEntireStreamAsString();
            juce::var uploadJson = juce::JSON::parse(uploadResponse);

            // juce::MessageManager::callAsync([uploadResponse] {
            //     juce::AlertWindow::showMessageBoxAsync(
            //         juce::AlertWindow::InfoIcon,
            //         "Upload Response",
            //         uploadResponse);
            // });

            if (!uploadJson.isObject())
                throw std::runtime_error("Invalid JSON from LALAL.AI API");

            auto* uploadObj = uploadJson.getDynamicObject();

            juce::String status = uploadObj->getProperty("status").toString();

            if (status != "success")
            {
                juce::String errorMsg = uploadObj->getProperty("error").toString();
                throw std::runtime_error(("LALAL.AI upload error: " + errorMsg).toStdString());
            }
            juce::String fileId = uploadObj->getProperty("id").toString();
            if (fileId.isEmpty())
            {
                juce::MessageManager::callAsync([this, file]()
                {
                    if (progressWindow) progressWindow.reset();
                    juce::AlertWindow::showMessageBoxAsync(
                        juce::AlertWindow::WarningIcon,
                        "The Audio file isn't successfully uploaded", "");
                });
            }
            // else
            // {
            //     juce::MessageManager::callAsync([this]()
            //     {
            //         if (progressWindow) progressWindow->setMessage("Processing...");
            //         juce::AlertWindow::showMessageBoxAsync(
            //             juce::AlertWindow::InfoIcon,
            //             "The Audio file is successfully uploaded", "");
            //     });
            // }

            // Pass the original file for naming and playback
            requestNoiseRemovalEdit(fileId);
        }
        catch (const std::exception& e)
        {
            busy = false;
            juce::MessageManager::callAsync([this, msg = juce::String("LALAL.AI Error: ") + e.what()]()
            {
                if (progressWindow) progressWindow.reset();
                juce::AlertWindow::showMessageBoxAsync(
                    juce::AlertWindow::WarningIcon,
                    "There is suddenly an error in LALAL.AI",
                    msg);
            });
        }
    }).detach();
}

void NoiseRemoverComponent::requestNoiseRemovalEdit(const juce::String &fileId)
{
    std::thread([this, fileId]()
    {
        juce::URL splitUrl("https://www.lalal.ai/api/split/");
        juce::String paramsJson = "[{\"id\":\"" + fileId + "\",\"filter\":2,\"stem\":\"vocals\"}]";
        juce::String postData = "params=" + paramsJson;

        auto splitOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inPostData)
            .withExtraHeaders("Authorization: license " + juce::String(apiKey) + "\n"
                              "Content-Type: application/x-www-form-urlencoded\n")
            .withHttpRequestCmd("POST");

        std::unique_ptr<juce::InputStream> splitStream = splitUrl.withPOSTData(postData).createInputStream(splitOptions);

        if (!splitStream)
        {
            busy = false;
            juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Error",
                    "Failed to start split job.");
            });
            return;
        }

        juce::String splitResponse = splitStream->readEntireStreamAsString();
        juce::var splitJson = juce::JSON::parse(splitResponse);

        // juce::MessageManager::callAsync([splitResponse] {
        //     juce::AlertWindow::showMessageBoxAsync(
        //         juce::AlertWindow::InfoIcon,
        //         "Split Response",
        //         splitResponse);
        // });

        if (splitJson.isArray() && splitJson.size() > 0)
        {
            auto* splitObj = splitJson[0].getDynamicObject();
            juce::String taskId = splitObj->getProperty("task_id").toString();
            if (taskId.isEmpty())
            {
                busy = false;
                juce::MessageManager::callAsync([] {
                    juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                        "Error",
                        "No job id in split response.");
                });
                return;
            }
            pollForResult(taskId, fileId);
        }
        else if (splitJson.isObject())
        {
            auto* splitObj = splitJson.getDynamicObject();
            juce::String taskId = splitObj->getProperty("task_id").toString();
            if (taskId.isNotEmpty())
            {
                pollForResult(taskId, fileId);
                return;
            }
            juce::String errorMsg = splitObj->getProperty("error").toString();
            busy = false;
            juce::MessageManager::callAsync([errorMsg] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Split Error",
                    errorMsg.isNotEmpty() ? errorMsg : "Invalid split response from LALAL.AI.");
            });
            return;
        }
        else
        {
            busy = false;
            juce::MessageManager::callAsync([] {
                juce::AlertWindow::showMessageBoxAsync(juce::AlertWindow::WarningIcon,
                    "Error",
                    "Invalid split response from LALAL.AI.");
            });
            return;
        }
    }).detach();
}

void NoiseRemoverComponent::pollForResult(const juce::String& taskId, const juce::String& fileId)
{


    juce::MessageManager::callAsync([this]() {
        if (!progressWindow)
        {
            progressWindow = std::make_unique<juce::AlertWindow>(
                "Progress",
                "Processing... 0%",
                juce::AlertWindow::InfoIcon);
            progressWindow->enterModalState();
        }
        else
        {
            progressWindow->setMessage("Processing... 0%");
        }
    });


    
    std::thread([this, taskId, fileId]()
    {
        const juce::String pollUrlBase = "https://www.lalal.ai/api/check/";
        while (true)
        {
                juce::URL pollUrl(pollUrlBase);
                auto pollOptions = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inPostData)
                    .withExtraHeaders("Authorization: license " + juce::String(apiKey) + "\n"
                                      "Content-Type: application/x-www-form-urlencoded\n")
                    .withHttpRequestCmd("POST");

                juce::String postData = "id=" + juce::URL::addEscapeChars(fileId, true);
                auto pollStream = pollUrl.withPOSTData(postData).createInputStream(pollOptions);

                juce::String pollResponse = pollStream->readEntireStreamAsString();
                juce::var pollJson = juce::JSON::parse(pollResponse);

                auto* pollObj = pollJson.getDynamicObject();
                juce::String status = pollObj->getProperty("status").toString();
                auto resultVar = pollObj->getProperty("result");
                auto* resultObj = resultVar.getDynamicObject();

                auto fileResultVar = resultObj->getProperty(fileId);


                auto* fileResultObj = fileResultVar.getDynamicObject();
                juce::String fileStatus = fileResultObj->getProperty("status").toString();


                auto taskVar = fileResultObj->getProperty("task");
                auto* taskObj = taskVar.getDynamicObject();
                juce::String taskState = taskObj->getProperty("state").toString();

               

                if(fileStatus == "success" && taskState == "success")
                {
                juce::String downloadUrl = "https://d.lalal.ai/media/split/" + fileId + "/" + taskId + "/vocals";
                // juce::String downloadUrl = "https://d.lalal.ai/media/split/6af00a36-9ce2-4da0-a102-e59bc945ba10/2a10bd10-5517-4703-ab82-3a48d527e049/vocals";
        
                juce::File outDir = juce::File::getSpecialLocation(juce::File::userDocumentsDirectory).getChildFile("AIpluginStatic");
                outDir.createDirectory(); // Ensure the directory exists

                // Compose output filename: originalfilename_stem.extension
                juce::String originalName = selectedFile.getFileNameWithoutExtension();
                juce::String extension = selectedFile.getFileExtension();
                if (extension.isEmpty())
                    extension = ".wav"; // fallback if extension missing
                juce::String outFileName = originalName + "_" + "noise_remover" + extension;

                juce::File outFile = outDir.getNonexistentChildFile(outFileName, extension);
                downloadProcessedFile(downloadUrl, outFile.getFullPathName());
                break;
        
                }else if(fileStatus == "error")
                {
                    juce::String errorMsg = fileResultObj->getProperty("error").toString();
                    juce::MessageManager::callAsync([errorMsg]()
                    {
                        juce::AlertWindow::showMessageBoxAsync(
                            juce::AlertWindow::WarningIcon,
                            "Error",
                            "LALAL.AI returned an error: " + errorMsg);
                    });
                }
                else {
                    // auto taskVar = fileResultObj->getProperty("task");
                    if (taskVar.isObject())
                    {
                      
                        if (taskState == "progress")
                        {
                            int progress = (int)taskObj->getProperty("progress");
                            juce::MessageManager::callAsync([this, progress]()
                            {
                                if (!progressWindow)
                                {
                                    progressWindow = std::make_unique<juce::AlertWindow>(
                                        "Progress",
                                        "Processing... " + juce::String(progress) + "%",
                                        juce::AlertWindow::InfoIcon);
                                    progressWindow->addButton("Cancel", 0);
                                    progressWindow->enterModalState();
                                }
                                else
                                {
                                    progressWindow->setMessage("Processing... " + juce::String(progress) + "%");
                                    progressWindow->repaint();
                                }
                            });
                            std::this_thread::sleep_for(std::chrono::seconds(1));
                            continue;
                        }
                        else if (taskState == "error")
                        {
                            juce::String errorMsg = taskObj->getProperty("error").toString();
                            juce::MessageManager::callAsync([errorMsg]()
                                {
                                    juce::AlertWindow::showMessageBoxAsync(
                                        juce::AlertWindow::WarningIcon,
                                        "Error",
                                        "LALAL.AI task error: " + errorMsg);
                                });
                            busy = false;
                            return;
                        }
                    }
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                }
        }
        busy = false;
    }).detach();
}

void NoiseRemoverComponent::downloadProcessedFile(const juce::String& downloadUrl, const juce::String& outputPath)
{
    try
    {
        // Set up browser-like headers for the download request
        juce::String headers = 
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\n"
            "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\n"
            "Accept-Language: en-US,en;q=0.9\n"
            "Connection: keep-alive\n"
            "Sec-Fetch-Dest: document\n"
            "Sec-Fetch-Mode: navigate\n"
            "Sec-Fetch-Site: none\n"
            "Sec-Fetch-User: ?1\n"
            "Upgrade-Insecure-Requests: 1\n";

        auto options = juce::URL::InputStreamOptions(juce::URL::ParameterHandling::inAddress)
            .withExtraHeaders(headers)
            .withConnectionTimeoutMs(30000)  // 30 second timeout
            .withStatusCode(nullptr);

        // Create the download stream
        std::unique_ptr<juce::InputStream> downloadStream = juce::URL(downloadUrl).createInputStream(options);
        
        if (!downloadStream)
        {
            throw std::runtime_error("Failed to create download stream");
        }

        // Create output file
        juce::File outputFile(outputPath);
        if (outputFile.existsAsFile())
            outputFile.deleteFile();

        std::unique_ptr<juce::FileOutputStream> fileStream(outputFile.createOutputStream());
        if (!fileStream)
        {
            throw std::runtime_error("Failed to create output file");
        }

        // Read the first few bytes to check content type (to detect HTML error pages)
        juce::MemoryBlock header(64);
        downloadStream->read(header.getData(), 64);
        juce::String headerStr(static_cast<const char*>(header.getData()), 64);
        
        if (headerStr.containsIgnoreCase("<!DOCTYPE html>") || headerStr.containsIgnoreCase("<html"))
        {
            throw std::runtime_error("Received HTML instead of audio file - download failed");
        }

        // Write the header we read
        fileStream->write(header.getData(), header.getSize());

        // Copy the rest of the data
        constexpr int bufferSize = 8192;
        juce::HeapBlock<char> buffer(bufferSize);
        
        while (!downloadStream->isExhausted())
        {
            auto bytesRead = downloadStream->read(buffer, bufferSize);
            if (bytesRead < 0)
                break;
            
            if (bytesRead > 0)
                fileStream->write(buffer, static_cast<size_t>(bytesRead));
        }

        fileStream->flush();
        juce::MessageManager::callAsync([this]()
        {
                    if (progressWindow)
                        progressWindow.reset();
        });

        if (playerBarComponent)
            playerBarComponent->loadAndPlayFile(outputFile);


        juce::MessageManager::callAsync([this, outputPath]()
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::InfoIcon,
                "Download Complete",
                "File saved to:\n" + outputPath);
        });
    }
    catch (const std::exception& e)
    {
        juce::String errorMsg = "Download failed: ";
        errorMsg += e.what();
        juce::MessageManager::callAsync([errorMsg]()
        {
            juce::AlertWindow::showMessageBoxAsync(
                juce::AlertWindow::WarningIcon,
                "Download Error",
                errorMsg);
        });
    }
}

