#pragma once

#include <JuceHeader.h>
#include "PlayerBarComponent.h"

class UploadIconButton : public juce::Button
{
public:
    UploadIconButton() : juce::But<PERSON>("UploadIconButton") {}

    void setIcon(std::unique_ptr<juce::Drawable> iconToUse)
    {
        icon = std::move(iconToUse);
        repaint();
    }

    void paintButton(juce::Graphics& g, bool isMouseOver, bool isButtonDown) override
    {
        auto bounds = getLocalBounds().toFloat();

        // Draw background
        auto bgColour = findColour(juce::TextButton::buttonColourId);
        if (isButtonDown)
            bgColour = bgColour.darker(0.1f);
        else if (isMouseOver)
            bgColour = bgColour.brighter(0.07f);

        g.setColour(bgColour);
        g.fillRoundedRectangle(bounds, 8.0f);

        // No border

        // Draw icon centered
        if (icon)
        {
            int iconSize = juce::jmin((int)bounds.getWidth(), (int)bounds.getHeight()) - 16;
            int iconX = (int)bounds.getCentreX() - iconSize / 2;
            int iconY = (int)bounds.getCentreY() - iconSize / 2;
            icon->drawWithin(g, juce::Rectangle<float>((float)iconX, (float)iconY, (float)iconSize, (float)iconSize),
                             juce::RectanglePlacement::centred, 1.0f);
        }
    }

private:
    std::unique_ptr<juce::Drawable> icon;
};



// Custom button for instrument selection (icon + text)
class CustomDrawableButton : public juce::TextButton
{
public:
    CustomDrawableButton(const juce::String& name);
    void setIcon(std::unique_ptr<juce::Drawable> iconToUse);
    juce::Font getFontToUse() const;
    void paintButton(juce::Graphics& g, bool isMouseOver, bool isButtonDown) override;

private:
    std::unique_ptr<juce::Drawable> icon;
};

// Main UI component for Split Stems page
class SplitSteamPageComponent : public juce::Component,
                                public juce::Button::Listener
{
public:
    SplitSteamPageComponent();
    ~SplitSteamPageComponent() override;

    void paint(juce::Graphics& g) override;
    void resized() override;
    void buttonClicked(juce::Button* button) override;
    void startSplitStemProcess(const juce::File &file);
    void requestSplitStems(const juce::String &uploadedFileId, const juce::String& stem);
    void pollForResult(const juce::String& taskId, const juce::String& uploadedFileId, const juce::String& stem);
    void downloadProcessedFile(const juce::String& downloadUrl, const juce::String& outputPath);
    PlayerBarComponent* playerBarComponent = nullptr;


private:
    void setupIconButton(CustomDrawableButton& button, juce::Drawable* icon, const juce::String& tooltip);

    // Upload area
    juce::Label uploadTitleLabel;
    juce::Label uploadSubtitleLabel;
    UploadIconButton uploadIconButton;
    std::unique_ptr<juce::Drawable> icon_upload;

    // Instrument buttons and icons
    CustomDrawableButton guitarButton{"Guitar"};
    CustomDrawableButton pianoButton{"Piano"};
    CustomDrawableButton vocalsButton{"Vocals"};
    CustomDrawableButton bassButton{"Bass"};
    CustomDrawableButton drumsButton{"Drums"};
    CustomDrawableButton otherButton{"Other"};

    std::unique_ptr<juce::Drawable> icon_guitar, icon_piano, icon_vocals, icon_bass, icon_drums, icon_other;

    // Split Stems button and icon
    CustomDrawableButton splitStemsButton{"SplitStems"};
    std::unique_ptr<juce::Drawable> icon_mini_music;

    // File chooser for upload
    std::unique_ptr<juce::FileChooser> fileChooser;
    std::unique_ptr<juce::AlertWindow> progressWindow;
    juce::File selectedFile;

    juce::String uploadedFileId;
    juce::String uploadedFileName;
    juce::String status;


    bool fileUploaded = false;
    // JUCE_DECLARE_NON_COPYABLE_WITH_LEAK_DETECTOR(SplitSteamPageComponent)
};

