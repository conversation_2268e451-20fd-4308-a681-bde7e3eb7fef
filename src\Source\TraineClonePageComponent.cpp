#include "TraineClonePageComponent.h"
#include "BinaryData.h"

// RecordingButton implementation
RecordingButton::RecordingButton(const juce::String &text)
    : juce::TextButton(text)
{
}

void RecordingButton::paintButton(juce::Graphics &g, bool isMouseOver, bool isButtonDown)
{
    juce::TextButton::paintButton(g, isMouseOver, isButtonDown);

    int dotDiameter = 18;
    int dotX = 16;
    int dotY = (getHeight() - dotDiameter) / 2;

    g.setColour(juce::Colours::red);
    g.fillEllipse((float)dotX, (float)dotY, (float)dotDiameter, (float)dotDiameter);
}

// TrainClonePageComponent implementation
TrainClonePageComponent::TrainClonePageComponent()
{
    addAndMakeVisible(startRecordingButton);
    addAndMakeVisible(uploadAudioButton);

    uploadAudioButton.setButtonText("Upload Audio");
    startRecordingButton.setColour(juce::TextButton::buttonColourId, juce::Colours::darkgrey);
    uploadAudioButton.setColour(juce::TextButton::buttonColourId, juce::Colours::darkgrey);
    startRecordingButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    uploadAudioButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    micImage = juce::ImageCache::getFromMemory(BinaryData::icon_micro_png, BinaryData::icon_micro_pngSize);

    startRecordingButton.onClick = [this]
    {
        selectedButton = 0;
        updateButtonColours();
    };
    uploadAudioButton.onClick = [this]
    {
        selectedButton = 1;
        updateButtonColours();
    };

    updateButtonColours();
}

void TrainClonePageComponent::updateButtonColours()
{
    juce::Colour selectedColour = juce::Colours::darkgrey;
    juce::Colour unselectedColour = juce::Colour(0xffbfc2c5);

    startRecordingButton.setColour(
        juce::TextButton::buttonColourId,
        selectedButton == 0 ? selectedColour : unselectedColour);
    uploadAudioButton.setColour(
        juce::TextButton::buttonColourId,
        selectedButton == 1 ? selectedColour : unselectedColour);

    startRecordingButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);
    uploadAudioButton.setColour(juce::TextButton::textColourOffId, juce::Colours::white);

    repaint();
}

void TrainClonePageComponent::paint(juce::Graphics &g)
{
    int sidebarWidth = 33;
    int marginTop = 20;
    int marginRight = 20;

    int bgX = sidebarWidth;
    int bgY = marginTop;
    int bgW = getWidth() - sidebarWidth - marginRight;
    int bgH = getHeight() - marginTop;

    g.setColour(juce::Colour(0xffbfc2c5));
    g.fillRect(bgX, bgY, bgW, bgH);

    int maxContentWidth = 500;
    int contentX = bgX + (bgW - maxContentWidth) / 2;
    int contentW = juce::jmin(bgW, maxContentWidth);

    int micSize = 48;
    int micX = getWidth() / 2 - micSize / 2;
    int micY = bgY + 8;
    if (micImage.isValid())
        g.drawImageWithin(micImage, micX, micY, micSize, micSize, juce::RectanglePlacement::centred);

    g.setColour(juce::Colours::dimgrey);
    g.setFont(juce::Font(28.0f, juce::Font::bold));
    g.drawText("CLONE YOUR VOICE INSTANTLY", contentX, micY + micSize + 20, contentW, 72, juce::Justification::centred);

    g.setFont(juce::Font(18.0f));
    g.setColour(juce::Colours::dimgrey);

    juce::String subtitle =
        "Create high quality AI clones of human voices instantly. Upload "
        "15-second snippet or record your own voice and you will have "
        "realistic clone of the voice within seconds";

    int subtitleY = micY + micSize + 70;
    int subtitleHeight = 3 * 26;
    g.drawFittedText(subtitle, contentX, subtitleY, contentW, subtitleHeight, juce::Justification::centred, 3);
}

void TrainClonePageComponent::resized()
{
    int maxContentWidth = 400;
    int contentX = (getWidth() - maxContentWidth) / 2;
    int contentW = juce::jmin(getWidth(), maxContentWidth);

    int buttonW = 180, buttonH = 56, gap = 24;
    int y = 240;
    int totalW = buttonW * 2 + gap;
    int x = contentX + (contentW - totalW) / 2;

    startRecordingButton.setBounds(x, y, buttonW, buttonH);
    uploadAudioButton.setBounds(x + buttonW + gap, y, buttonW, buttonH);
}