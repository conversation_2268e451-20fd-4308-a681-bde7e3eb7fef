#include "MainPanelComponent.h"
#include "BinaryData.h"

void MainPanelComponent::showPage(int pageIndex)
{
    if (createPage)
        createPage->setVisible(pageIndex == 0);
    if (libraryPage)
        libraryPage->setVisible(pageIndex == 1);
    if (trainClonePage)
        trainClonePage->setVisible(pageIndex == 2);
    if (keyBpmFinderPage)
        keyBpmFinderPage->setVisible(pageIndex == 3);
    if (noiseRemoverPage)
        noiseRemoverPage->setVisible(pageIndex == 4);
    if (splitSteamPage)
        splitSteamPage->setVisible(pageIndex == 5);
    if (mixMasterPage)
        mixMasterPage->setVisible(pageIndex == 6);
}

MainPanelComponent::MainPanelComponent()
{
    addAndMakeVisible(playerBar);
    addAndMakeVisible(sidebarComponent);

    searchBox.setTextToShowWhenEmpty("search", juce::Colours::grey);
    searchBox.setFont(juce::Font(22.0f)); // Increase from 18.0f to 22.0f (or larger if you prefer)
    searchBox.setColour(juce::TextEditor::backgroundColourId, juce::Colours::transparentWhite);
    searchBox.setColour(juce::TextEditor::outlineColourId, juce::Colours::transparentBlack);
    searchBox.setColour(juce::TextEditor::focusedOutlineColourId, juce::Colours::transparentBlack);
    searchBox.setColour(juce::TextEditor::textColourId, juce::Colours::black);

    addAndMakeVisible(searchBox);

    logoImage = juce::ImageCache::getFromMemory(BinaryData::render_mark_png, BinaryData::render_mark_pngSize);

    createPage = std::make_unique<CreatePageComponent>();
    addAndMakeVisible(*createPage);
    createPage->setVisible(false);

    trainClonePage = std::make_unique<TrainClonePageComponent>();
    addAndMakeVisible(*trainClonePage);
    trainClonePage->setVisible(false);

    keyBpmFinderPage = std::make_unique<KeyBpmFinderComponent>();
    addAndMakeVisible(*keyBpmFinderPage);
    keyBpmFinderPage->setVisible(false);

    noiseRemoverPage = std::make_unique<NoiseRemoverComponent>();
    noiseRemoverPage->playerBarComponent = &playerBar;
    addAndMakeVisible(*noiseRemoverPage);
    noiseRemoverPage->setVisible(false);

    splitSteamPage = std::make_unique<SplitSteamPageComponent>();
    splitSteamPage->playerBarComponent = &playerBar;
    addAndMakeVisible(*splitSteamPage);
    splitSteamPage->setVisible(false);

    mixMasterPage = std::make_unique<MixMasterPageComponent>();
    addAndMakeVisible(*mixMasterPage);
    mixMasterPage->setVisible(false);

    libraryPage = std::make_unique<LibraryPageComponent>();
    addAndMakeVisible(*libraryPage);
    libraryPage->setVisible(false);

    sidebarComponent.onButtonClicked = [this](int pageIndex)
    {
        showPage(pageIndex);
    };
}

void MainPanelComponent::paint(juce::Graphics &g)
{
    juce::ColourGradient gradient(
        juce::Colour::fromRGB(40, 40, 55), 0, 0,
        juce::Colour::fromRGB(30, 30, 40), 0, (float)getHeight(), false);
    gradient.addColour(0.5, juce::Colour::fromRGB(60, 60, 80));
    gradient.addColour(1.0, juce::Colour::fromRGB(30, 30, 40));
    g.setGradientFill(gradient);
    g.fillAll();

    int sidebarWidth = 240;
    int logoX = sidebarWidth + 20;
    int logoY = 20;
    int logoWidth = 220;
    int logoHeight = 48;
    if (!logoImage.isNull())
        g.drawImageWithin(logoImage, logoX, logoY, logoWidth, logoHeight, juce::RectanglePlacement::centred);

    int searchBoxHeight = 40;
    int searchBoxX = logoX + logoWidth + 20;
    int searchBoxY = logoY + (logoHeight - searchBoxHeight) / 2;
    int searchBoxWidth = getWidth() - searchBoxX - 40;
    float searchBoxRadius = 10.0f;

    g.setColour(juce::Colours::lightgrey);
    g.fillRoundedRectangle((float)searchBoxX, (float)searchBoxY, (float)searchBoxWidth, (float)searchBoxHeight, searchBoxRadius);

    g.setColour(juce::Colours::grey.withAlpha(0.3f));
    // g.drawRoundedRectangle((float)searchBoxX, (float)searchBoxY, (float)searchBoxWidth, (float)searchBoxHeight, searchBoxRadius, 2.0f);
}

void MainPanelComponent::resized()
{
    int logoX = 20;
    int logoY = 20;
    int logoWidth = 220;
    int logoHeight = 48;
    int sidebarWidth = 240;

    int searchBoxHeight = 40;
    int searchBoxX = logoX + logoWidth + 20;
    int searchBoxY = logoY + (logoHeight - searchBoxHeight) / 2;
    int searchBoxWidth = getWidth() - searchBoxX - 40;

    sidebarComponent.setBounds(0, 0, sidebarWidth, getHeight());

    int contentX = sidebarWidth;
    int contentWidth = getWidth() - sidebarWidth;
    int contentHeight = getHeight();

    searchBox.setBounds(
        contentX + searchBoxX, searchBoxY,
        contentWidth - searchBoxX - 20, searchBoxHeight);

    int playerBarHeight = 200;
    playerBar.setBounds(contentX, getHeight() - playerBarHeight, contentWidth, playerBarHeight);

    int pageY = 80;
    int pageHeight = getHeight() - 230;
    if (createPage)
        createPage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (libraryPage)
        libraryPage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (trainClonePage)
        trainClonePage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (keyBpmFinderPage)
        keyBpmFinderPage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (noiseRemoverPage)
        noiseRemoverPage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (splitSteamPage)
        splitSteamPage->setBounds(contentX, pageY, contentWidth, pageHeight);
    if (mixMasterPage)
        mixMasterPage->setBounds(contentX, pageY, contentWidth, pageHeight);
}