#pragma once
#include <JuceHeader.h>
// #include <aubio/aubio.h>    

// Custom LookAndFeel to center table header text
class CenteredHeaderLookAndFeel : public juce::LookAndFeel_V4
{
public:
    void drawTableHeaderColumn(juce::Graphics& g, juce::TableHeaderComponent& header,
                               const juce::String& columnName, int /*columnId*/,
                               int width, int height, bool isMouseOver, bool isButtonDown,
                               int /*columnFlags*/) override;
};

// Custom drop area with dotted border and icon
class DropAreaComponent : public juce::Component
{
public:
    DropAreaComponent(const juce::Image& iconToUse)
        : icon(iconToUse)
    {
        setInterceptsMouseClicks(true, true); // Ensure mouse events are received
    }

    void paint(juce::Graphics& g) override;

    std::function<void()> onClicked;

    void mouseUp(const juce::MouseEvent& event) override;

private:
    juce::Image icon;
};

// Main Key/BPM Finder component
class KeyBpmFinderComponent : public juce::Component, public juce::TableListBoxModel
{
public:
    KeyBpmFinderComponent();
    void resized() override;

    // Table model methods
    int getNumRows() override;
    void paintRowBackground(juce::Graphics& g, int rowNumber, int width, int height, bool rowIsSelected) override;
    void paintCell(juce::Graphics& g, int rowNumber, int columnId, int width, int height, bool rowIsSelected) override;

private:
    static juce::Image loadMusicIcon();

    DropAreaComponent dropArea;

    struct SeparatorLine : public juce::Component
    {
        void paint(juce::Graphics& g) override;
    } separatorLine;

    juce::TableListBox table;

    struct FileData
    {
        juce::String fileName, key, altKey, bpm, energy; // Add energy
    };
    std::vector<FileData> fileData;

    void openFileDialogAndProcess();

    std::unique_ptr<juce::FileChooser> chooser;
    juce::File selectedFile;

    // Add this line:
    void processFileWithAubio(const juce::File& file);
};